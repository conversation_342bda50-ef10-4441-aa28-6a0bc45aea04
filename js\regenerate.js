async function regenerateMetadata(rowData, type = 'all', skipConfirmation = false) {
    if (!rowData || !rowData.name) {
        console.error("Invalid row data for regeneration", rowData);
        showToast("Error: Invalid row data", "error");
        return;
    }
    if (isProcessing) {
        showToast("Cannot regenerate while main processing is active.", "warning");
        return;
    }
    if (!skipConfirmation) {
        const message = type === 'all'
            ? `Are you sure you want to regenerate all metadata for "${rowData.name}"?`
            : `Are you sure you want to regenerate ${type} for "${rowData.name}"?`;
        showConfirmModal(message, async () => {
            await processRegeneration(rowData, type);
        });
    } else {
        await processRegeneration(rowData, type);
    }
}
async function processRegeneration(rowData, type) {
    const itemIndex = metadataResults.findIndex(item => item.name === rowData.name);
    if (itemIndex === -1) {
        console.error("Item not found in metadata results", rowData.name);
        showToast("Error: Item not found", "error");
        return;
    }

    const mediaFile = images.find(img => img.name === rowData.name);
    if (!mediaFile) {
        console.error("Media file not found for regeneration", rowData.name);
        showToast("Error: Media file not found", "error");
        return;
    }

    // Check if this is a video file
    const isVideo = isVideoFile(mediaFile) || rowData.isVideo === true;
    console.log(`Processing regeneration for ${rowData.name}, isVideo: ${isVideo}`);

    const originalTitle = metadataResults[itemIndex].title;
    const originalDescription = metadataResults[itemIndex].description;
    const originalKeywords = metadataResults[itemIndex].keywords;
    const originalShutterstockCategories = metadataResults[itemIndex].shutterstockCategories;
    const originalAdobestockCategory = metadataResults[itemIndex].adobestockCategory;
    const controller = new AbortController();
    const signal = controller.signal;

    try {
        updateTableWithMetadata(itemIndex, null, "Processing...");

        // Prepare the media file for processing
        let processedMediaFile = mediaFile;

        // If this is a video, ensure it's properly handled
        if (isVideo) {
            console.log("Preparing video for regeneration");
            // Always use the original video file for direct video processing
            processedMediaFile.isVideo = true;
            console.log("Using original video file for regeneration");
        }

        // Pass the type parameter to callGeminiAPI with the properly prepared media file
        let metadata = await callGeminiAPI(processedMediaFile, true, signal, type);

        let attempts = 1;
        const maxAttempts = 2;
        if (signal.aborted) throw new DOMException('Aborted', 'AbortError');

        while (attempts < maxAttempts) {
            let needsRegeneration = false;
            if ((type === 'all' || type === 'description') && metadata.description.trim() === originalDescription.trim() && originalDescription.trim() !== "") needsRegeneration = true;
            if ((type === 'all' || type === 'keywords') && metadata.keywords.trim() === originalKeywords.trim() && originalKeywords.trim() !== "") needsRegeneration = true;
            if ((type === 'all' || type === 'title') && metadata.title.trim() === originalTitle.trim() && originalTitle.trim() !== "") needsRegeneration = true;
            if ((type === 'all' || type === 'shutterstockCategories') && metadata.shutterstockCategories && metadata.shutterstockCategories.trim() === originalShutterstockCategories.trim() && originalShutterstockCategories.trim() !== "") needsRegeneration = true;
            if ((type === 'all' || type === 'adobestockCategory') && metadata.adobestockCategory && metadata.adobestockCategory.trim() === originalAdobestockCategory.trim() && originalAdobestockCategory.trim() !== "") needsRegeneration = true;

            if (!needsRegeneration) break;

            showToast(`Regeneration attempt ${attempts + 1}...`, "info");
            // Pass the type parameter to callGeminiAPI with the properly prepared media file
            metadata = await callGeminiAPI(processedMediaFile, true, signal, type);
            if (signal.aborted) throw new DOMException('Aborted', 'AbortError');
            attempts++;
        }
        const updatePayload = {};
        if (type === 'all' || type === 'title') updatePayload.title = metadata.title;
        if (type === 'all' || type === 'description') updatePayload.description = cleanDescriptionText(metadata.description);
        if (type === 'all' || type === 'keywords') {
            const maxKeywords = getMaxKeywordsFromSettings();
            updatePayload.keywords = cleanKeywordsFormat(metadata.keywords, maxKeywords);
        }
        if (type === 'all' || type === 'shutterstockCategories') {
            if (metadata.shutterstockCategories) {
                updatePayload.shutterstockCategories = metadata.shutterstockCategories;
            }
        }
        if (type === 'all' || type === 'adobestockCategory') {
            if (metadata.adobestockCategory) {
                updatePayload.adobestockCategory = metadata.adobestockCategory;
            }
        }
        if (signal.aborted) throw new DOMException('Aborted', 'AbortError');

        // Preserve the isVideo flag in the metadata
        updatePayload.isVideo = isVideo;

        updateTableWithMetadata(itemIndex, updatePayload, "Completed");

        // Show appropriate success message based on media type
        if (type === 'all') {
            showToast(`Metadata regenerated successfully for ${isVideo ? 'video' : 'image'}`);
        } else {
            let fieldName = type;
            if (type === 'shutterstockCategories') fieldName = 'Categories';
            else if (type === 'adobestockCategory') fieldName = 'Category';
            else fieldName = type.charAt(0).toUpperCase() + type.slice(1);

            showToast(`${fieldName} regenerated successfully for ${isVideo ? 'video' : 'image'}`);
        }

        if (table) {
            const row = table.getRow(rowData.name);
            if (row) row.deselect();
        }
    } catch (error) {
        console.error("Error regenerating metadata:", error);
        let status;
        if (error.name === 'AbortError') {
            status = "Stopped";
            showToast("Regeneration stopped", "info");
        } else {
            status = `Error: ${error.message || "Regeneration failed"}`;
            showToast("Error regenerating metadata", "error");
        }
        updateTableWithMetadata(itemIndex, null, status);
    }
}
async function regenerateAllRows() {
    if (isProcessing) {
         showToast("Cannot start regeneration while main processing is active.", "warning");
         return;
    }
    if (!metadataResults || metadataResults.length === 0) {
        showToast("No data to process", "warning");
        return;
    }

    // Count videos and images
    const videoCount = metadataResults.filter(item => item.isVideo).length;
    const imageCount = metadataResults.length - videoCount;

    // Create appropriate message based on media types
    let mediaDescription = "";
    if (imageCount > 0 && videoCount > 0) {
        mediaDescription = `${imageCount} image${imageCount !== 1 ? 's' : ''} and ${videoCount} video${videoCount !== 1 ? 's' : ''}`;
    } else if (videoCount > 0) {
        mediaDescription = `${videoCount} video${videoCount !== 1 ? 's' : ''}`;
    } else {
        mediaDescription = `${imageCount} image${imageCount !== 1 ? 's' : ''}`;
    }

    showConfirmModal(
        `Are you sure you want to regenerate all metadata for ${mediaDescription}? This process may take a long time.`,
        async () => {
            metadataResults.forEach(item => { item.status = 'Queued'; });
            if (table) table.setData(metadataResults);
            await generateMetadata();
        }
    );
}
async function regenerateSelectedRows() {
     if (isProcessing) {
         showToast("Cannot start regeneration while main processing is active.", "warning");
         return;
    }
    if (!table) {
        showToast("Table not initialized", "error");
        return;
    }
    const selectedRows = table.getSelectedRows();
    if (selectedRows.length === 0) {
        showToast("No rows selected", "warning");
        return;
    }

    // Count videos and images in the selected rows
    const selectedData = selectedRows.map(row => row.getData());
    const videoCount = selectedData.filter(item => item.isVideo).length;
    const imageCount = selectedData.length - videoCount;

    // Create appropriate message based on media types
    let mediaDescription = "";
    if (imageCount > 0 && videoCount > 0) {
        mediaDescription = `${imageCount} image${imageCount !== 1 ? 's' : ''} and ${videoCount} video${videoCount !== 1 ? 's' : ''}`;
    } else if (videoCount > 0) {
        mediaDescription = `${videoCount} video${videoCount !== 1 ? 's' : ''}`;
    } else {
        mediaDescription = `${imageCount} image${imageCount !== 1 ? 's' : ''}`;
    }

    showConfirmModal(
        `Are you sure you want to regenerate metadata for ${mediaDescription}? This process may take a long time.`,
        async () => {
             const selectedNames = selectedRows.map(row => row.getData().name);
             metadataResults.forEach(item => {
                 if (selectedNames.includes(item.name)) {
                     item.status = 'Queued';
                 }
             });
             if (table) table.setData(metadataResults);
             await generateMetadata();
        }
    );
}

async function regenerateErrorRows() {
    if (isProcessing) {
        showToast("Cannot start regeneration while main processing is active.", "warning");
        return;
    }
    if (!metadataResults || metadataResults.length === 0) {
        showToast("No data to process", "warning");
        return;
    }

    // Find rows with error status
    const errorRows = metadataResults.filter(item => item.status && item.status.startsWith("Error"));
    console.log(`regenerateErrorRows: Found ${errorRows.length} rows with error status`);

    if (errorRows.length === 0) {
        showToast("No rows with errors found", "info");
        return;
    }

    // Count videos and images in the error rows
    const videoCount = errorRows.filter(item => item.isVideo).length;
    const imageCount = errorRows.length - videoCount;

    // Create appropriate message based on media types
    let mediaDescription = "";
    if (imageCount > 0 && videoCount > 0) {
        mediaDescription = `${imageCount} image${imageCount !== 1 ? 's' : ''} and ${videoCount} video${videoCount !== 1 ? 's' : ''}`;
    } else if (videoCount > 0) {
        mediaDescription = `${videoCount} video${videoCount !== 1 ? 's' : ''}`;
    } else {
        mediaDescription = `${imageCount} image${imageCount !== 1 ? 's' : ''}`;
    }

    showConfirmModal(
        `Are you sure you want to regenerate metadata for ${mediaDescription} with errors? This process may take a long time.`,
        async () => {
            // Mark error rows as queued
            let queuedCount = 0;
            metadataResults.forEach(item => {
                if (item.status && item.status.startsWith("Error")) {
                    item.status = 'Queued';
                    queuedCount++;
                }
            });
            console.log(`regenerateErrorRows: Marked ${queuedCount} rows as Queued`);

            if (table) table.setData(metadataResults);
            await generateMetadata();
        }
    );
}

// Make functions available globally
window.regenerateMetadata = regenerateMetadata;
window.regenerateAllRows = regenerateAllRows;
window.regenerateSelectedRows = regenerateSelectedRows;
window.regenerateErrorRows = regenerateErrorRows;