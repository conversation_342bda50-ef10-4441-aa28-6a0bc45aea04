/**
 * Gemini Video API Module
 * Handles direct video processing with Gemini API
 */

// Import required functions from other modules
function getApiKey() {
    return localStorage.getItem('csvision_api_key') || '';
}

/**
 * Limit categories to maximum 2 categories
 * @param {string} categories - The categories string
 * @returns {string} - Categories limited to maximum 2
 */
function limitCategoriesToTwo(categories) {
    if (!categories || typeof categories !== 'string') {
        return '';
    }

    // Split by comma and clean up each category
    const categoryArray = categories.split(',')
        .map(cat => cat.trim())
        .filter(cat => cat.length > 0);

    // Limit to maximum 2 categories
    const limitedCategories = categoryArray.slice(0, 2);

    return limitedCategories.join(', ');
}

// Use the global showToast function if available, otherwise create a fallback
function showToast(message, type = 'info', duration = 3000) {
    if (typeof window.showToast === 'function') {
        window.showToast(message, type, duration);
    } else {
        console.log(`[${type.toUpperCase()}] ${message}`);
    }
}

/**
 * Process a video file directly with Gemini API
 * @param {File} videoFile - The video file to process
 * @param {string} prompt - The prompt to use for video analysis
 * @param {AbortSignal} signal - Optional AbortSignal for cancellation
 * @returns {Promise<Object>} - Promise resolving to the API response
 */
async function processVideoWithGemini(videoFile, prompt, signal = null) {
    try {
        console.log(`Starting direct video processing for: ${videoFile.name} (${videoFile.size} bytes, ${videoFile.type})`);

        const apiKey = getApiKey();
        if (!apiKey) {
            console.error("API key is missing. Cannot process video.");
            throw new Error("API key is required. Please enter your Gemini API key in the Settings tab.");
        }

        // Get the selected model from localStorage or use a default
        let selectedModel = localStorage.getItem('csvision_model') || 'gemini-2.0-flash';
        console.log(`Selected model for video processing: ${selectedModel}`);

        // Ensure we're using a model that supports video
        if (!selectedModel.startsWith('gemini-2.0')) {
            console.warn(`Model ${selectedModel} does not support video. Switching to gemini-2.0-flash.`);
            selectedModel = 'gemini-2.0-flash';
            // Update localStorage with the video-compatible model
            localStorage.setItem('csvision_model', selectedModel);
        }

        // Check video file size (Gemini has a 20MB limit)
        const maxSizeBytes = 20 * 1024 * 1024; // 20MB
        if (videoFile.size > maxSizeBytes) {
            console.warn(`Video file size (${(videoFile.size / (1024 * 1024)).toFixed(2)}MB) exceeds Gemini's 20MB limit.`);
            showToast(`Warning: Video file is larger than 20MB. Processing may fail.`, "warning");
        }

        // Convert video file to base64
        const base64Video = await new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = () => resolve(reader.result.split(',')[1]);
            reader.onerror = reject;
            reader.readAsDataURL(videoFile);
        });

        // Prepare request data for direct video processing
        const requestData = {
            contents: [{
                parts: [
                    {
                        inlineData: {
                            mimeType: videoFile.type,
                            data: base64Video
                        }
                    },
                    {
                        text: prompt
                    }
                ]
            }],
            generation_config: {
                temperature: 0.4,
                max_output_tokens: 1024
            }
        };

        console.log(`Sending video analysis request to model: ${selectedModel}`);
        console.log(`Prompt for video analysis: ${prompt.substring(0, 100)}...`);

        const analysisResponse = await fetch(`https://generativelanguage.googleapis.com/v1/models/${selectedModel}:generateContent?key=${apiKey}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestData),
            signal: signal
        });

        if (!analysisResponse.ok) {
            const errorText = await analysisResponse.text();
            console.error("Video analysis error:", errorText);
            showToast(`Error analyzing video: ${analysisResponse.status} ${analysisResponse.statusText}`, "error");
            throw new Error(`Failed to analyze video: ${analysisResponse.status} ${analysisResponse.statusText}`);
        }

        const analysisData = await analysisResponse.json();
        console.log("Video analysis completed successfully");
        showToast(`Video analysis completed successfully`, "success");

        // Log a sample of the response for debugging
        if (analysisData.candidates && analysisData.candidates[0] && analysisData.candidates[0].content) {
            const parts = analysisData.candidates[0].content.parts;
            if (parts && parts.length > 0) {
                const responseText = parts[0].text || "";
                console.log(`Video analysis response sample: ${responseText.substring(0, 100)}...`);
            }
        }

        return analysisData;
    } catch (error) {
        console.error("Error processing video with Gemini:", error);

        // Check if this is a model compatibility issue
        if (error.message && error.message.includes("not supported")) {
            console.warn("Model compatibility issue detected. Switching to gemini-2.0-flash model.");
            localStorage.setItem('csvision_model', 'gemini-2.0-flash');
            showToast("Switched to Gemini 2.0 Flash model for video support. Please try again.", "warning");
        }

        // Check if this is a file size issue
        if (error.message && (error.message.includes("too large") || error.message.includes("size limit"))) {
            showToast("Video file is too large for Gemini API. Maximum size is 20MB.", "error");
        }

        throw error;
    }
}

/**
 * Generate metadata for a video using direct video processing
 * @param {File} videoFile - The video file to process
 * @param {AbortSignal} signal - Optional AbortSignal for cancellation
 * @param {string} type - Type of metadata to generate ('all', 'title', 'description', 'keywords')
 * @returns {Promise<Object>} - Promise resolving to metadata object
 */
async function generateVideoMetadata(videoFile, signal = null, type = 'all') {
    try {
        console.log(`Generating metadata for video: ${videoFile.name}, type: ${type}`);
        console.log(`Video file details: size=${videoFile.size}, type=${videoFile.type}, fromVideo=${videoFile.fromVideo || false}`);

        // Log additional information about the video file
        if (videoFile.videoMetadata) {
            console.log(`Video has metadata: duration=${videoFile.videoMetadata.duration}, dimensions=${videoFile.videoMetadata.width}x${videoFile.videoMetadata.height}`);
        }

        // Get prompt settings and processing parameters from localStorage
            const titleLength = parseInt(localStorage.getItem('csvision_title_length')) || 10;
            const positiveTitlePrompt = localStorage.getItem('csvision_positive_title_prompt') || "";
            const negativeTitlePrompt = localStorage.getItem('csvision_negative_title_prompt') || "";

            // Get current description length from the slider element
            const descriptionLengthSlider = document.getElementById('descriptionLengthSlider');
            const descriptionLength = descriptionLengthSlider ? parseInt(descriptionLengthSlider.value) : 250;
            // This is character count, not word count
            console.log(`Using current description length from slider: ${descriptionLength} characters (strict limit)`);

            // Convert character count to approximate word count for the prompt
            // Average English word is ~5-6 characters, so divide by 6 for a conservative estimate
            const approximateWordCount = Math.max(Math.floor(descriptionLength / 6), 10); // Minimum 10 words
            console.log(`Converted to approximate word count: ${approximateWordCount} words`);
            const positiveDescriptionPrompt = localStorage.getItem('csvision_positive_description_prompt') || "";
            const negativeDescriptionPrompt = localStorage.getItem('csvision_negative_description_prompt') || "";

            const keywordsCount = parseInt(localStorage.getItem('csvision_keywords_count')) || 20;
            const positiveKeywordsPrompt = localStorage.getItem('csvision_positive_keywords_prompt') || "";
            const negativeKeywordsPrompt = localStorage.getItem('csvision_negative_keywords_prompt') || "";

            // Get processing parameters from localStorage
            const enableApiKeyRotation = localStorage.getItem('csvision_enable_api_key_rotation') === 'true';
            const selectedModel = localStorage.getItem('csvision_model') || 'gemini-2.0-flash';
            const concurrency = parseInt(localStorage.getItem('csvision_concurrency')) || 3;
            const rateLimit = parseInt(localStorage.getItem('csvision_rate_limit')) || 5;
            const enableParallelProcessing = localStorage.getItem('csvision_enable_parallel_processing') !== 'false';
            const useDirectVideo = localStorage.getItem('csvision_use_direct_video') !== 'false';

            // Apply processing parameters
            if (!useDirectVideo) {
                console.warn("Direct video processing is disabled in settings");
                showToast("Direct video processing is disabled. Enable it in Settings for full video analysis.", "warning");
            }

            if (!selectedModel.startsWith('gemini-2.0')) {
                console.warn(`Model ${selectedModel} does not support video. Switching to gemini-2.0-flash.`);
                localStorage.setItem('csvision_model', 'gemini-2.0-flash');
                showToast("Switched to Gemini 2.0 Flash model for video support.", "warning");
            }

            // Build the enhanced prompt based on metadata type
            let enhancedPrompt = "Analyze this video and provide the following information:\n";

            if (type === 'title') {
                enhancedPrompt += `1. Title: ${positiveTitlePrompt || "Create a concise, descriptive title"}. Exactly ${titleLength} words.`;
                if (negativeTitlePrompt) {
                    enhancedPrompt += `\n\nDO NOT: ${negativeTitlePrompt}`;
                }
            } else if (type === 'description') {
                enhancedPrompt += `1. Description: ${positiveDescriptionPrompt || "Write a detailed description"}. The description should be approximately ${approximateWordCount} words to fit within ${descriptionLength} characters. This is a strict character limit requirement.`;
                if (negativeDescriptionPrompt) {
                    enhancedPrompt += `\n\nDO NOT: ${negativeDescriptionPrompt}`;
                }
            } else if (type === 'keywords') {
                enhancedPrompt += `1. Keywords: ${positiveKeywordsPrompt || "Generate relevant keywords"}. Exactly ${keywordsCount} terms, separated by commas.`;
                if (negativeKeywordsPrompt) {
                    enhancedPrompt += `\n\nDO NOT: ${negativeKeywordsPrompt}`;
                }
            } else {
                // Define category lists for video
                const videoCategoriesList = "Animals/Wildlife, Arts, Backgrounds/Textures, Buildings/Landmarks, Business/Finance, Education, Food and drink, Healthcare/Medical, Holidays, Industrial, Nature, Objects, People, Religion, Science, Signs/Symbols, Sports/Recreation, Technology, Transportation";
                const adobeStockCategoriesList = "1. Animals, 2. Buildings and Architecture, 3. Business, 4. Drinks, 5. The Environment, 6. States of Mind, 7. Food, 8. Graphic Resources, 9. Hobbies and Leisure, 10. Industry, 11. Landscapes, 12. Lifestyle, 13. People, 14. Plants and Flowers, 15. Culture and Religion, 16. Science, 17. Social Issues, 18. Sports, 19. Technology, 20. Transport, 21. Travel";

                enhancedPrompt += `1. Title: ${positiveTitlePrompt || "Create a concise, descriptive title"}. Exactly ${titleLength} words.\n`;
                if (negativeTitlePrompt) {
                    enhancedPrompt += `DO NOT for title: ${negativeTitlePrompt}\n\n`;
                }

                enhancedPrompt += `2. Description: ${positiveDescriptionPrompt || "Write a detailed description"}. The description should be approximately ${approximateWordCount} words to fit within ${descriptionLength} characters. This is a strict character limit requirement.\n`;
                if (negativeDescriptionPrompt) {
                    enhancedPrompt += `DO NOT for description: ${negativeDescriptionPrompt}\n\n`;
                }

                enhancedPrompt += `3. Keywords: ${positiveKeywordsPrompt || "Generate relevant keywords"}. Exactly ${keywordsCount} terms, separated by commas.\n`;
                if (negativeKeywordsPrompt) {
                    enhancedPrompt += `DO NOT for keywords: ${negativeKeywordsPrompt}\n\n`;
                }

                enhancedPrompt += `4. Categories: Analyze the PRIMARY FUNCTION and PURPOSE of this video content, then select 1-2 categories from this video categories list (${videoCategoriesList}). Focus on what the video is USED FOR or its INTENDED PURPOSE, not just what objects are visible. Examples: A video showing a laptop should be "Technology" or "Business/Finance" based on its context, not "Objects". A cooking video should be "Food and drink" regardless of kitchen tools shown.\n`;
                enhancedPrompt += `5. Category: Based on the video's main FUNCTION and CONTEXT, select only one category number from this Adobe Stock list (${adobeStockCategoriesList}).`;
            }

            enhancedPrompt += "\n\nFormat the response as JSON with title, description, keywords, shutterstockCategories, and adobestockCategory fields.";

        console.log("Video metadata prompt:", enhancedPrompt);

        // Process the video with Gemini
        const response = await processVideoWithGemini(videoFile, enhancedPrompt, signal);

        // Extract the text from the response
        let responseText = "";
        if (response.candidates && response.candidates[0] && response.candidates[0].content) {
            const parts = response.candidates[0].content.parts;
            if (parts && parts.length > 0) {
                responseText = parts[0].text || "";
            }
        }

        // Log the full response text for debugging
        console.log("Full response text from Gemini:", responseText);

        // Try to parse the response as JSON
        let metadata = {};
        try {
            // First, try to extract JSON from the text if it's not already JSON
            const jsonMatch = responseText.match(/```json\\n(.+?)\\n```/s) ||
                             responseText.match(/```json\n(.+?)\n```/s) ||
                             responseText.match(/```\n(.+?)\n```/s) ||
                             responseText.match(/{.+}/s);

            const jsonText = jsonMatch ? jsonMatch[1] || jsonMatch[0] : responseText;
            metadata = JSON.parse(jsonText);

            // Log the raw metadata before processing
            console.log("Raw metadata from JSON response:", metadata);

            // Limit categories to maximum 2 if present
            if (metadata.shutterstockCategories) {
                metadata.shutterstockCategories = limitCategoriesToTwo(metadata.shutterstockCategories);
            }

            // Log the parsed metadata for debugging
            console.log("Successfully parsed metadata from JSON response:", metadata);
        } catch (parseError) {
            console.error("Error parsing JSON response:", parseError);

            // If JSON parsing fails, try to extract information using regex
            console.log("Attempting regex extraction from response text");

            const titleMatch = responseText.match(/Title:?\s*(.+?)(?:\n|$)/i);
            const descriptionMatch = responseText.match(/Description:?\s*(.+?)(?:\n|$)/i);
            const keywordsMatch = responseText.match(/Keywords:?\s*(.+?)(?:\n|$)/i);
            const categoriesMatch = responseText.match(/Categories:?\s*(.+?)(?:\n|$)/i);
            const categoryMatch = responseText.match(/Category:?\s*(.+?)(?:\n|$)/i);

            metadata = {
                title: titleMatch ? titleMatch[1].trim() : "",
                description: descriptionMatch ? descriptionMatch[1].trim() : "",
                keywords: keywordsMatch ? keywordsMatch[1].trim() : "",
                shutterstockCategories: categoriesMatch ? limitCategoriesToTwo(categoriesMatch[1].trim()) : "",
                adobestockCategory: categoryMatch ? categoryMatch[1].trim().replace(/^\d+\.\s*/, '') : ""
            };

            console.log("Extracted metadata using regex fallback:", metadata);
        }

        // Ensure metadata fields exist and are strings
        if (!metadata.title && (type === 'all' || type === 'title')) {
            console.warn("No title returned from API");
            metadata.title = '';
        } else if (metadata.title && typeof metadata.title !== 'string') {
            console.warn(`Title is not a string: ${typeof metadata.title}`);
            metadata.title = String(metadata.title);
        }

        if (!metadata.description && (type === 'all' || type === 'description')) {
            console.warn("No description returned from API");
            metadata.description = '';
        } else if (metadata.description && typeof metadata.description !== 'string') {
            console.warn(`Description is not a string: ${typeof metadata.description}`);
            metadata.description = String(metadata.description);
        }

        // Log the description length before any adjustments
        if (metadata.description) {
            const wordCount = metadata.description.split(/\s+/).filter(word => word.length > 0).length;
            console.log(`Raw description word count before adjustment: ${wordCount}/${descriptionLength}`);
        }

        // Strictly enforce description length limit based on character count
        if (metadata.description) {
            // Check character count
            const charCount = metadata.description.length;
            console.log(`Raw description character count: ${charCount}/${descriptionLength}`);

            // Enforce character limit based on slider value
            if (charCount > descriptionLength) {
                // First try to trim at a sentence or punctuation boundary
                let trimmedDesc = metadata.description.substring(0, descriptionLength);

                // Try to find the last sentence boundary
                const lastPeriod = trimmedDesc.lastIndexOf('.');
                const lastQuestion = trimmedDesc.lastIndexOf('?');
                const lastExclamation = trimmedDesc.lastIndexOf('!');

                // Find the last sentence boundary that's at least 60% of the way through
                const minPosition = Math.floor(descriptionLength * 0.6);
                let boundaryPos = -1;

                if (lastPeriod > minPosition) boundaryPos = Math.max(boundaryPos, lastPeriod);
                if (lastQuestion > minPosition) boundaryPos = Math.max(boundaryPos, lastQuestion);
                if (lastExclamation > minPosition) boundaryPos = Math.max(boundaryPos, lastExclamation);

                // If we found a good sentence boundary, use it
                if (boundaryPos > 0) {
                    trimmedDesc = trimmedDesc.substring(0, boundaryPos + 1);
                } else {
                    // Otherwise, try to trim at a word boundary
                    const lastSpace = trimmedDesc.lastIndexOf(' ');
                    if (lastSpace > 0) {
                        trimmedDesc = trimmedDesc.substring(0, lastSpace);
                    }
                }

                metadata.description = trimmedDesc;
                console.log(`Trimmed description from ${charCount} to ${metadata.description.length} characters to match slider limit`);
                showToast(`Description trimmed to fit within ${descriptionLength} character limit`, "info");
            } else if (charCount < descriptionLength * 0.7) {
                // If description is significantly shorter than the limit, log a warning
                console.log(`Warning: Description is shorter than expected (${charCount}/${descriptionLength} characters)`);
                showToast(`Description is shorter than the requested ${descriptionLength} character length`, "warning");
            }

            // Log word count for reference - ensure description is a string first
            if (typeof metadata.description === 'string') {
                const wordCount = metadata.description.split(/\s+/).filter(word => word.length > 0).length;
                console.log(`Final description word count: ${wordCount} words (approx. ${approximateWordCount} requested)`);
            } else {
                console.warn("Cannot calculate word count: description is not a string");
            }
        }

        // Limit the number of keywords
        if (metadata.keywords) {
            // Make sure keywords is a string before trying to split it
            if (typeof metadata.keywords === 'string') {
                const keywords = metadata.keywords.split(',').map(k => k.trim());
                metadata.keywords = keywords.slice(0, keywordsCount).join(', ');
            } else {
                console.warn(`Keywords is not a string: ${typeof metadata.keywords}`, metadata.keywords);
                // Ensure keywords is a string
                metadata.keywords = metadata.keywords ? String(metadata.keywords) : '';
            }
        } else if (type === 'keywords') {
            // If we're specifically requesting keywords but none were returned, provide an empty string
            console.warn("No keywords returned from API but keywords were requested");
            metadata.keywords = '';
        }

        // Ensure category fields exist for 'all' type
        if (type === 'all') {
            // Debug: Log all available fields in metadata
            console.log("Available fields in metadata:", Object.keys(metadata));

            if (!metadata.shutterstockCategories) {
                console.warn("No shutterstockCategories returned from API");
                // Check if Gemini used a different field name
                if (metadata.categories) {
                    console.log("Found 'categories' field instead, using it:", metadata.categories);
                    metadata.shutterstockCategories = limitCategoriesToTwo(metadata.categories);
                } else if (metadata.category) {
                    console.log("Found 'category' field instead, using it:", metadata.category);
                    metadata.shutterstockCategories = limitCategoriesToTwo(metadata.category);
                } else {
                    metadata.shutterstockCategories = '';
                }
            }
            if (!metadata.adobestockCategory) {
                console.warn("No adobestockCategory returned from API");
                metadata.adobestockCategory = '';
            }

            // Clean up adobestockCategory to extract just the number
            if (metadata.adobestockCategory && typeof metadata.adobestockCategory === 'string') {
                const numberMatch = metadata.adobestockCategory.match(/^(\d+)/);
                if (numberMatch) {
                    metadata.adobestockCategory = numberMatch[1];
                }
            }

            // Final check: Limit categories to maximum 2
            if (metadata.shutterstockCategories && typeof metadata.shutterstockCategories === 'string') {
                metadata.shutterstockCategories = limitCategoriesToTwo(metadata.shutterstockCategories);
                console.log("Final shutterstockCategories after limiting:", metadata.shutterstockCategories);
            }
        }

        // Final check for description length after all adjustments
        if (metadata.description) {
            const finalCharCount = metadata.description.length;
            console.log(`Final description character count after all adjustments: ${finalCharCount}/${descriptionLength}`);

            // Double-check that we're not exceeding the character limit
            if (finalCharCount > descriptionLength) {
                console.warn(`Warning: Final description still exceeds character limit. Performing final trim.`);
                // Trim at word boundary if possible
                let trimmedDesc = metadata.description.substring(0, descriptionLength);
                const lastSpace = trimmedDesc.lastIndexOf(' ');
                if (lastSpace > 0) {
                    trimmedDesc = trimmedDesc.substring(0, lastSpace);
                }
                metadata.description = trimmedDesc;
                console.log(`Final trim: description now ${metadata.description.length} characters`);
            }

            // Log final word count for reference - ensure description is a string first
            if (typeof metadata.description === 'string') {
                const finalWordCount = metadata.description.split(/\s+/).filter(word => word.length > 0).length;
                console.log(`Final word count: ${finalWordCount} words (target was approx. ${approximateWordCount})`);
            } else {
                console.warn("Cannot calculate final word count: description is not a string");
            }
        }

        console.log("Successfully generated metadata for video:", metadata);
        return metadata;
    } catch (error) {
        console.error("Error generating video metadata:", error);

        // Check if this is a model compatibility issue
        if (error.message && error.message.includes("not supported")) {
            console.warn("Model compatibility issue detected. Switching to gemini-2.0-flash model.");
            localStorage.setItem('csvision_model', 'gemini-2.0-flash');
            showToast("Switched to Gemini 2.0 Flash model for video support. Please try again.", "warning");
        }

        // Check if direct video processing is disabled
        const useDirectVideo = localStorage.getItem('csvision_use_direct_video') !== 'false';
        if (!useDirectVideo) {
            showToast("Direct video processing is disabled. Enable it in Settings for full video analysis.", "warning");
        }

        throw error;
    }
}

// Export functions
window.processVideoWithGemini = processVideoWithGemini;
window.generateVideoMetadata = generateVideoMetadata;
