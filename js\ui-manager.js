function setupEventListeners() {
    // Setup regular file input (all media)
    const folderInput = document.getElementById('folderInput');
    if (folderInput) {
        folderInput.addEventListener('change', (e) => handleFileSelection(e, 'all'));
    }

    // Setup folder selection button (all media)
    const selectFolderBtn = document.getElementById('selectFolderBtn');
    const folderDirectoryInput = document.getElementById('folderDirectoryInput');
    if (selectFolderBtn && folderDirectoryInput) {
        selectFolderBtn.addEventListener('click', () => {
            folderDirectoryInput.click();
        });
        folderDirectoryInput.addEventListener('change', (e) => handleFolderSelection(e, 'all'));
    }

    // Setup images only input
    const imagesOnlyInput = document.getElementById('imagesOnlyInput');
    if (imagesOnlyInput) {
        imagesOnlyInput.addEventListener('change', (e) => handleFileSelection(e, 'images'));
    }

    // Setup videos only input
    const videosOnlyInput = document.getElementById('videosOnlyInput');
    if (videosOnlyInput) {
        videosOnlyInput.addEventListener('change', (e) => handleFileSelection(e, 'videos'));
    }

    // Setup folder images only input
    const folderImagesOnlyInput = document.getElementById('folderImagesOnlyInput');
    if (folderImagesOnlyInput) {
        folderImagesOnlyInput.addEventListener('change', (e) => handleFolderSelection(e, 'images'));
    }

    // Setup folder videos only input
    const folderVideosOnlyInput = document.getElementById('folderVideosOnlyInput');
    if (folderVideosOnlyInput) {
        folderVideosOnlyInput.addEventListener('change', (e) => handleFolderSelection(e, 'videos'));
    }

    const generateBtn = document.getElementById('generateBtn');
    if (generateBtn) {
        generateBtn.addEventListener('click', generateMetadata);
    }
    const pauseBtn = document.getElementById('pauseBtn');
    if (pauseBtn) {
        pauseBtn.addEventListener('click', pauseProcessing);
    }
    const stopProcessBtn = document.getElementById('stopProcessBtn');
    if (stopProcessBtn) {
        stopProcessBtn.addEventListener('click', stopProcessing);
    }
    const exportBtn = document.getElementById('exportBtn');
    if (exportBtn) {
        exportBtn.addEventListener('click', exportToCSV);
    }
    const clearBtn = document.getElementById('clearBtn');
    if (clearBtn) {
        clearBtn.addEventListener('click', clearData);
    }
    // Worker message event listener removed as parallel processing now handles workers efficiently
}
// Function to add an image to metadata - can be called from other modules
window.addImageToMetadata = function(file) {
    if (!file) {
        console.error("No file provided to addImageToMetadata");
        return false;
    }

    // Check if file is already in the images array
    if (images.some(img => img.name === file.name)) {
        console.log(`File ${file.name} already exists in images array`);
        return false;
    }

    // Check if this is from imagen-subtab
    if (file.fromImagen) {
        console.log(`File ${file.name} is from imagen-subtab`);

        // Preserve the fromImagen flag when adding to images array
        // This will be used in api.js to handle API key rotation correctly
    }

    // Add to images array
    images.push(file);

    // Create a new row for the metadata table
    const newRow = {
        name: file.name,
        preview: `<img src="${URL.createObjectURL(file)}" class="thumbnail">`,
        title: "",
        description: "",
        keywords: "",
        shutterstockCategories: "",
        adobestockCategory: "",
        status: "Pending",
        startTime: null,
        processingTime: 0,
        isVideo: false,
        fromImagen: file.fromImagen || false // Track if this is from imagen-subtab
    };

    // Add to metadataResults array
    metadataResults.push(newRow);

    // Add to table if it exists
    if (table) {
        table.addData([newRow])
            .then(() => {
                updateUIState();
                return true;
            })
            .catch(error => {
                console.error("Error adding data to table:", error);
                return false;
            });
    } else {
        updateUIState();
    }

    return true;
};

/**
 * Handle file selection with optional filtering
 * @param {Event} event - The change event from the file input
 * @param {string} filterType - The type of filter to apply: 'all', 'images', or 'videos'
 */
async function handleFileSelection(event, filterType = 'all') {
    const files = event.target.files;
    let selectedFiles = [];

    // Apply filter based on filterType
    if (filterType === 'images') {
        selectedFiles = Array.from(files).filter(file => file.type.startsWith('image/'));
        if (selectedFiles.length === 0) {
            showToast("No image files found. Please select at least one image file.", "warning");
            updateUIState();
            return;
        }
    } else if (filterType === 'videos') {
        selectedFiles = Array.from(files).filter(file => file.type.startsWith('video/'));
        if (selectedFiles.length === 0) {
            showToast("No video files found. Please select at least one video file.", "warning");
            updateUIState();
            return;
        }
    } else {
        // Default: all media files
        selectedFiles = Array.from(files).filter(file =>
            file.type.startsWith('image/') || file.type.startsWith('video/')
        );
        if (selectedFiles.length === 0) {
            showToast("No media files found. Please select at least one image or video file.", "warning");
            updateUIState();
            return;
        }
    }

    // Check for GIF files and show a notification
    const gifFiles = selectedFiles.filter(file => file.type === 'image/gif');
    if (gifFiles.length > 0) {
        showToast("GIF files detected. They will be automatically converted to PNG format for processing.", "info", 5000);
    }

    // Check for video files and show a notification
    const videoFiles = selectedFiles.filter(file => file.type.startsWith('video/'));
    if (videoFiles.length > 0) {
        showToast(`${videoFiles.length} video file(s) detected. Thumbnails will be extracted for processing.`, "info", 5000);
    }

    const existingFileNames = images.map(img => img.name);
    const newFiles = selectedFiles.filter(file => !existingFileNames.includes(file.name));
    if (newFiles.length === 0) {
        showToast("All selected files are already added.", "warning");
        event.target.value = '';
        return;
    }
    const newRowsData = [];

    // Process files one by one, handling videos specially
    for (const file of newFiles) {
        if (metadataResults.some(item => item.name === file.name)) continue;

        // Check if the file is a video
        if (isVideoFile(file)) {
            try {
                // Show processing status
                showToast(`Processing video: ${file.name}`, "info");

                // Process the video file (extract thumbnail and metadata)
                const processedVideo = await processVideoForAPI(file);

                // Add the original video to the images array with metadata
                file.videoMetadata = processedVideo.metadata;
                file.thumbnailFile = processedVideo.thumbnail;
                images.push(file);

                // Create a row with video information
                const newRow = {
                    name: file.name,
                    preview: `<div class="video-thumbnail-container">
                                <img src="${URL.createObjectURL(processedVideo.thumbnail)}" class="thumbnail">
                                <span class="video-duration">${formatDuration(processedVideo.metadata.duration)}</span>
                                <i class="bi bi-film video-icon"></i>
                             </div>`,
                    title: "",
                    description: "",
                    keywords: "",
                    shutterstockCategories: "",
                    adobestockCategory: "",
                    status: "Pending",
                    startTime: null,
                    processingTime: 0,
                    isVideo: true,
                    videoMetadata: processedVideo.metadata
                };

                metadataResults.push(newRow);
                newRowsData.push(newRow);
            } catch (error) {
                console.error(`Error processing video ${file.name}:`, error);
                showToast(`Error processing video: ${file.name}`, "error");
            }
        } else {
            // Handle regular image files
            images.push(file);
            const newRow = {
                name: file.name,
                preview: `<img src="${URL.createObjectURL(file)}" class="thumbnail">`,
                title: "",
                description: "",
                keywords: "",
                shutterstockCategories: "",
                adobestockCategory: "",
                status: "Pending",
                startTime: null,
                processingTime: 0,
                isVideo: false
            };
            metadataResults.push(newRow);
            newRowsData.push(newRow);
        }
    }

    /**
     * Format duration in seconds to MM:SS format
     * @param {number} seconds - Duration in seconds
     * @returns {string} - Formatted duration string
     */
    function formatDuration(seconds) {
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = Math.floor(seconds % 60);
        return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
    }
    if (table && newRowsData.length > 0) {
        table.addData(newRowsData)
            .then(() => {
                updateUIState();
            })
            .catch(error => {
                console.error("Error adding data to table:", error);
                showToast("Error updating table.", "error");
                updateUIState();
            });
    } else {
        updateUIState();
    }
    if (newFiles.length > 0) {
        showToast(`Added ${newFiles.length} new image${newFiles.length > 1 ? 's' : ''} successfully`, "success");
    }
    // Clear the file input but keep the status label updated
    event.target.value = '';
}
function clearData() {
        if (isProcessing) {
            stopProcessing();
            showToast("Processing stop requested. Please confirm clear again once stopped.", "info");
            return;
        }
    showConfirmModal(
        "Are you sure you want to delete all data? This action cannot be undone.",
        () => {
            if (isProcessing) {
                 showToast("Processing is still active. Cannot clear data.", "warning");
                 return;
            }
            metadataResults.forEach(item => {
                const imgElement = document.createElement('div');
                imgElement.innerHTML = item.preview;
                const imgSrc = imgElement.querySelector('img')?.src;
                if (imgSrc && imgSrc.startsWith('blob:')) {
                    URL.revokeObjectURL(imgSrc);
                }
            });
            images = [];
            metadataResults = [];
            if (table) {
                table.setData([]);
            }
            const fileInput = document.getElementById('folderInput');
            if (fileInput) fileInput.value = '';

            updateUIState();
            updateExportButtonsState();
            showToast("All data has been cleared", "info");
        }
    );
}
function updateUIState() {
    const hasData = images.length > 0;
    const clearBtn = document.getElementById('clearBtn');
    const folderInput = document.getElementById('folderInput');
    const generateBtn = document.getElementById('generateBtn');
    const applyMetadataBtn = document.getElementById('applyMetadataBtn');
    const disableActions = isProcessing || !hasData;
    if (clearBtn) clearBtn.disabled = isProcessing;
    if (generateBtn) generateBtn.disabled = disableActions;
    if (applyMetadataBtn) applyMetadataBtn.disabled = disableActions;

    // Update file status label
    updateFileStatusLabel();

    // Update export buttons state
    updateExportButtonsState();
}

/**
 * Handle folder selection and process files in the folder with optional filtering
 * @param {Event} event - The change event from the folder input
 * @param {string} filterType - The type of filter to apply: 'all', 'images', or 'videos'
 */
async function handleFolderSelection(event, filterType = 'all') {
    const files = event.target.files;
    if (!files || files.length === 0) {
        showToast("No files found in the selected folder", "warning");
        return;
    }

    // Get folder name from the first file's path
    const folderPath = files[0].webkitRelativePath;
    const folderName = folderPath.split('/')[0];

    // Apply filter based on filterType
    let mediaFiles = [];

    if (filterType === 'images') {
        mediaFiles = Array.from(files).filter(file => file.type.startsWith('image/'));
        if (mediaFiles.length === 0) {
            showToast("No image files found in the selected folder", "warning");
            return;
        }
        showToast(`Found ${mediaFiles.length} image file(s) in folder "${folderName}"`, "info");
    } else if (filterType === 'videos') {
        mediaFiles = Array.from(files).filter(file => file.type.startsWith('video/'));
        if (mediaFiles.length === 0) {
            showToast("No video files found in the selected folder", "warning");
            return;
        }
        showToast(`Found ${mediaFiles.length} video file(s) in folder "${folderName}"`, "info");
    } else {
        // Default: all media files
        mediaFiles = Array.from(files).filter(file =>
            file.type.startsWith('image/') || file.type.startsWith('video/')
        );
        if (mediaFiles.length === 0) {
            showToast("No media files found in the selected folder", "warning");
            return;
        }
    }

    // Check for GIF files and show a notification
    const gifFiles = mediaFiles.filter(file => file.type === 'image/gif');
    if (gifFiles.length > 0) {
        showToast(`${gifFiles.length} GIF file(s) detected. They will be automatically converted to PNG format for processing.`, "info", 5000);
    }

    // Check for video files and show a notification
    const videoFiles = mediaFiles.filter(file => file.type.startsWith('video/'));
    if (videoFiles.length > 0 && filterType !== 'videos') {
        showToast(`${videoFiles.length} video file(s) detected. Thumbnails will be extracted for processing.`, "info", 5000);
    }

    // Check for duplicates
    const existingFileNames = images.map(img => img.name);
    const newFiles = mediaFiles.filter(file => !existingFileNames.includes(file.name));

    if (newFiles.length === 0) {
        showToast("All files from this folder are already added", "warning");
        event.target.value = '';
        return;
    }

    showToast(`Processing ${newFiles.length} files from folder: ${folderName}`, "info");

    // Process the new files
    const newRowsData = [];

    // Process files one by one, handling videos specially
    for (const file of newFiles) {
        if (metadataResults.some(item => item.name === file.name)) continue;

        // Check if the file is a video
        if (isVideoFile(file)) {
            try {
                // Show processing status
                showToast(`Processing video: ${file.name}`, "info");

                // Process the video file (extract thumbnail and metadata)
                const processedVideo = await processVideoForAPI(file);

                // Add the original video to the images array with metadata
                file.videoMetadata = processedVideo.metadata;
                file.thumbnailFile = processedVideo.thumbnail;
                images.push(file);

                // Create a row with video information
                const newRow = {
                    name: file.name,
                    preview: `<div class="video-thumbnail-container">
                                <img src="${URL.createObjectURL(processedVideo.thumbnail)}" class="thumbnail">
                                <span class="video-duration">${formatDuration(processedVideo.metadata.duration)}</span>
                                <i class="bi bi-film video-icon"></i>
                             </div>`,
                    title: "",
                    description: "",
                    keywords: "",
                    shutterstockCategories: "",
                    adobestockCategory: "",
                    status: "Pending",
                    startTime: null,
                    processingTime: 0,
                    isVideo: true,
                    videoMetadata: processedVideo.metadata
                };

                metadataResults.push(newRow);
                newRowsData.push(newRow);
            } catch (error) {
                console.error(`Error processing video ${file.name}:`, error);
                showToast(`Error processing video: ${file.name}`, "error");
            }
        } else {
            // Handle regular image files
            images.push(file);
            const newRow = {
                name: file.name,
                preview: `<img src="${URL.createObjectURL(file)}" class="thumbnail">`,
                title: "",
                description: "",
                keywords: "",
                shutterstockCategories: "",
                adobestockCategory: "",
                status: "Pending",
                startTime: null,
                processingTime: 0,
                isVideo: false
            };
            metadataResults.push(newRow);
            newRowsData.push(newRow);
        }
    }

    /**
     * Format duration in seconds to MM:SS format
     * @param {number} seconds - Duration in seconds
     * @returns {string} - Formatted duration string
     */
    function formatDuration(seconds) {
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = Math.floor(seconds % 60);
        return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
    }

    if (table && newRowsData.length > 0) {
        table.addData(newRowsData)
            .then(() => {
                updateUIState();
            })
            .catch(error => {
                console.error("Error adding data to table:", error);
                showToast("Error updating table.", "error");
                updateUIState();
            });
    } else {
        updateUIState();
    }

    if (newFiles.length > 0) {
        showToast(`Added ${newFiles.length} new image${newFiles.length > 1 ? 's' : ''} from folder "${folderName}"`, "success");
    }

    // Clear the input value to allow selecting the same folder again
    event.target.value = '';
}

function updateFileStatusLabel() {
    const fileStatusLabel = document.getElementById('fileStatusLabel');
    if (!fileStatusLabel) return;

    if (images.length > 0) {
        // Count videos and images separately
        const videoCount = images.filter(file => isVideoFile(file)).length;
        const imageCount = images.length - videoCount;

        let statusText = '';

        if (imageCount > 0 && videoCount > 0) {
            // Both images and videos
            statusText = `<i class="bi bi-collection"></i> ${imageCount} image${imageCount > 1 ? 's' : ''}, ${videoCount} video${videoCount > 1 ? 's' : ''} selected`;
        } else if (videoCount > 0) {
            // Only videos
            statusText = `<i class="bi bi-film"></i> ${videoCount} video${videoCount > 1 ? 's' : ''} selected`;
        } else {
            // Only images
            statusText = `<i class="bi bi-images"></i> ${imageCount} image${imageCount > 1 ? 's' : ''} selected`;
        }

        fileStatusLabel.innerHTML = statusText;
        fileStatusLabel.classList.add('has-files');
    } else {
        fileStatusLabel.innerHTML = '<i class="bi bi-image"></i> No media selected';
        fileStatusLabel.classList.remove('has-files');
    }
}
let footerUpdateInterval = null;
function updateProcessingUI(isStarting = false) {
    const generateBtn = document.getElementById('generateBtn');
    const pauseBtn = document.getElementById('pauseBtn');
    const stopProcessBtn = document.getElementById('stopProcessBtn');
    const statusStopBtn = document.getElementById('statusStopBtn');
    const clearBtn = document.getElementById('clearBtn');
    if (isStarting) {
        if (generateBtn) generateBtn.classList.add('d-none');
        if (pauseBtn) {
            pauseBtn.classList.remove('d-none');
            pauseBtn.innerHTML = '<i class="bi bi-pause-fill"></i> Pause';
            pauseBtn.classList.remove('btn-success');
            pauseBtn.classList.add('btn-warning');
            isPaused = false;
        }
        if (stopProcessBtn) stopProcessBtn.classList.remove('d-none');
        if (statusStopBtn) statusStopBtn.style.display = 'inline-block';
        if (clearBtn) clearBtn.disabled = true;
        updateProgressUI(true);
        updateProcessingStatus(-1, images.length);
        if (footerUpdateInterval) {
            clearInterval(footerUpdateInterval);
        }
        footerUpdateInterval = setInterval(updateTableFooter, 1000);
    } else {
        if (generateBtn) generateBtn.classList.remove('d-none');
        if (pauseBtn) pauseBtn.classList.add('d-none');
        if (stopProcessBtn) stopProcessBtn.classList.add('d-none');
        if (statusStopBtn) statusStopBtn.style.display = 'none';
        if (clearBtn) clearBtn.disabled = (images.length === 0);
        updateProgressUI(false);
        if (footerUpdateInterval) {
            clearInterval(footerUpdateInterval);
            footerUpdateInterval = null;
        }
    }
    updateUIState();
}
function updateExportButtonsState() {
    const exportBtn = document.getElementById('exportBtn');
    const templateExportBtns = document.querySelectorAll('.template-export-btn');
    const hasData = metadataResults && metadataResults.length > 0;
    const disableExport = isProcessing || !hasData;
    if (exportBtn) {
        exportBtn.disabled = disableExport;
    }
    templateExportBtns.forEach(btn => {
        btn.disabled = disableExport;
    });
}
function updateProcessingStatus(currentIndex, totalImages) {
    const progressBar = document.getElementById('progressBar');
    const progressStatus = document.getElementById('progressStatus');
    if (progressBar && progressStatus) {
        const processedCount = currentIndex + 1;
        const percentage = totalImages > 0 ? Math.round((processedCount / totalImages) * 100) : 0;
        const currentFileName = (currentIndex >= 0 && currentIndex < images.length) ? images[currentIndex]?.name : "Starting...";
        progressBar.style.width = `${percentage}%`;
        progressBar.setAttribute('aria-valuenow', percentage);
        progressStatus.textContent = `Processing ${processedCount} of ${totalImages} (${percentage}%): ${currentFileName}`;
    }
}
function updateProgressUI(show = true) {
    const progressContainer = document.querySelector('.progress-container');
    if (!progressContainer) return;
    if (show) {
        progressContainer.classList.remove('d-none');
    } else {
        progressContainer.classList.add('d-none');
        const progressBar = document.getElementById('progressBar');
         const progressStatus = document.getElementById('progressStatus');
         if (progressBar) {
             progressBar.style.width = '0%';
             progressBar.setAttribute('aria-valuenow', 0);
         }
         if (progressStatus) {
             progressStatus.textContent = "Idle";
         }
    }
}
function updateParallelProgress(completed, total, result, fileName) {
    console.log("updateParallelProgress called with:", { completed, total, result, fileName });
    updateProcessingStatus(completed -1, total);
    if (!result) {
        return;
    }
    let targetIndex = -1;
    if (result.index !== undefined && result.index >= 0 && result.index < metadataResults.length) {
        targetIndex = result.index;
    }
    else if (fileName) {
        targetIndex = metadataResults.findIndex(r => r.name === fileName &&
            ["Processing", "Queued", "Retrying..."].includes(r.status));
        if (targetIndex === -1) {
            targetIndex = metadataResults.findIndex(r => r.name === fileName);
        }
    }
    if (targetIndex !== -1) {
        console.log(`Processing result for ${fileName} at index ${targetIndex}`);
        let status = "Error: Unknown";
        if (result.success) {
            status = "Completed";
        } else if (result.error) {
            if (result.error === "Processing stopped by user" || result.error === "Stopped by user") {
                status = "Stopped";
            } else if (result.retrying) {
                status = `Retrying... (${result.retryCount || 1}/3)`;
            } else {
                status = `Error: ${result.error}`;
            }
        }
        const metadata = result.success ? result.metadata : null;
        updateTableWithMetadata(targetIndex, metadata, status);
    } else {
        console.warn(`Could not find valid index for result: ${fileName}`, result);
    }
    if (completed < total) {
        const nextIndex = metadataResults.findIndex(item => item.status === "Queued");
        if (nextIndex !== -1) {
            updateTableWithMetadata(nextIndex, null, "Processing");
        }
    }
}
function updateTableWithMetadata(index, metadata, status = "Completed", retryCount = 0) {
    console.log("updateTableWithMetadata called with:", { index, status, retryCount });
    if (index < 0 || index >= metadataResults.length) {
        console.error("Invalid index provided to updateTableWithMetadata:", index);
        return;
    }
    const rowData = metadataResults[index];
    if (!rowData || !rowData.name) {
        console.error("Invalid row data at index:", index);
        return;
    }
    const fileName = rowData.name;
    console.log(`Updating metadata for ${fileName} at index ${index} with status ${status}`);
    if (metadata) {
        console.log(`Metadata for ${fileName}:`, {
            title: metadata.title ? metadata.title.substring(0, 20) + '...' : 'undefined',
            description: metadata.description ? metadata.description.substring(0, 20) + '...' : 'undefined',
            keywords: metadata.keywords ? metadata.keywords.substring(0, 20) + '...' : 'undefined'
        });
    }
    let dataUpdated = false;
    if (rowData.status !== "Stopped" || status !== "Stopped") {
        if (metadata) {
            if (metadata.title !== undefined && metadataResults[index].title !== metadata.title) {
                metadataResults[index].title = metadata.title; dataUpdated = true;
            }
            if (metadata.description !== undefined) {
                 const cleanedDesc = cleanDescriptionText(metadata.description);
                 if (metadataResults[index].description !== cleanedDesc) {
                     metadataResults[index].description = cleanedDesc; dataUpdated = true;
                 }
            }
            if (metadata.keywords !== undefined) {
                 const maxKeywords = getMaxKeywordsFromSettings();
                 const cleanedKeywords = cleanKeywordsFormat(metadata.keywords, maxKeywords);
                 if (metadataResults[index].keywords !== cleanedKeywords) {
                     metadataResults[index].keywords = cleanedKeywords; dataUpdated = true;
                 }
            }
            if (metadata.shutterstockCategories !== undefined) {
                 if (metadataResults[index].shutterstockCategories !== metadata.shutterstockCategories) {
                     metadataResults[index].shutterstockCategories = metadata.shutterstockCategories; dataUpdated = true;
                 }
            }
            if (metadata.adobestockCategory !== undefined) {
                 if (metadataResults[index].adobestockCategory !== metadata.adobestockCategory) {
                     metadataResults[index].adobestockCategory = metadata.adobestockCategory; dataUpdated = true;
                 }
            }
        }
        if (metadataResults[index].status !== status) {
            const currentTime = Date.now();
            const oldStatus = metadataResults[index].status;

            // Log status change for debugging
            console.log(`Status change for ${metadataResults[index].name}: ${oldStatus} -> ${status}`);

            // Handle status transitions
            if (oldStatus === "Processing" && status !== "Processing") {
                // Finishing processing - calculate processing time
                if (metadataResults[index].startTime) {
                    metadataResults[index].processingTime = (currentTime - metadataResults[index].startTime) / 1000;
                    console.log(`Processing time for ${metadataResults[index].name}: ${metadataResults[index].processingTime.toFixed(2)}s`);
                    metadataResults[index].startTime = null;
                }

                // If completed, record the end time
                if (status === "Completed") {
                    metadataResults[index].lastProcessedTime = currentTime;
                    console.log(`Completed time recorded for ${metadataResults[index].name}: ${new Date(currentTime).toISOString()}`);
                }
            }
            else if (status === "Processing") {
                // Starting processing
                metadataResults[index].startTime = currentTime;
                metadataResults[index].processingTime = 0;

                // Record first processed time if not already set
                if (!metadataResults[index].firstProcessedTime) {
                    metadataResults[index].firstProcessedTime = currentTime;
                    console.log(`First processing time recorded for ${metadataResults[index].name}: ${new Date(currentTime).toISOString()}`);
                }
            }
            else if (status === "Completed" && !metadataResults[index].lastProcessedTime) {
                // Ensure completed items always have a lastProcessedTime
                metadataResults[index].lastProcessedTime = currentTime;
                console.log(`Completed time recorded for ${metadataResults[index].name}: ${new Date(currentTime).toISOString()}`);

                // If we don't have a processing time, estimate it
                if (!metadataResults[index].processingTime && metadataResults[index].firstProcessedTime) {
                    metadataResults[index].processingTime = (currentTime - metadataResults[index].firstProcessedTime) / 1000;
                    console.log(`Estimated processing time for ${metadataResults[index].name}: ${metadataResults[index].processingTime.toFixed(2)}s`);
                }
            }

            metadataResults[index].status = status;
            dataUpdated = true;
        }
    } else {
         status = "Stopped";
         if (metadataResults[index].status !== status) {
              metadataResults[index].status = status;
              dataUpdated = true;
         }
    }
    if (dataUpdated && table && table.initialized) {
        try {
            const row = table.getRow(rowData.name);
            if (row) {
                const updateObject = { status: status };
                if (metadata && status === "Completed") {
                    if (metadata.title !== undefined) updateObject.title = metadataResults[index].title || '';
                    if (metadata.description !== undefined) updateObject.description = metadataResults[index].description || '';
                    if (metadata.keywords !== undefined) updateObject.keywords = metadataResults[index].keywords || '';
                    if (metadata.shutterstockCategories !== undefined) updateObject.shutterstockCategories = metadataResults[index].shutterstockCategories || '';
                    if (metadata.adobestockCategory !== undefined) updateObject.adobestockCategory = metadataResults[index].adobestockCategory || '';
                }

                // Ensure all required fields exist in updateObject
                if (!updateObject.hasOwnProperty('shutterstockCategories')) {
                    updateObject.shutterstockCategories = metadataResults[index].shutterstockCategories || '';
                }
                if (!updateObject.hasOwnProperty('adobestockCategory')) {
                    updateObject.adobestockCategory = metadataResults[index].adobestockCategory || '';
                }
                row.update(updateObject)
                   .then(() => {
                       console.log("Row updated successfully:", rowData.name);
                       const infoCell = row.getCell('info');
                       if (infoCell) infoCell.setValue(metadataResults[index]);
                       const element = row.getElement();
                       if (status === "Completed") element.style.backgroundColor = "#f8fff8";
                       else if (status.startsWith("Error")) element.style.backgroundColor = "#fff8f8";
                       else if (status === "Stopped") element.style.backgroundColor = "#fff8f0";
                       else if (status === "Paused") element.style.backgroundColor = "#f8f8ff";
                       else element.style.backgroundColor = "";
                   })
                   .catch(err => {
                        console.error(`Error updating row ${rowData.name} directly:`, err);
                        if (retryCount < 3) {
                            console.log(`Retrying update for ${rowData.name} (attempt ${retryCount + 1})`);
                            setTimeout(() => updateTableWithMetadata(index, metadata, status, retryCount + 1), 1000);
                        }
                   });
            } else {
                console.warn(`Row ${rowData.name} not found in table for update.`);
                if (retryCount < 3) {
                    console.log(`Retrying update for ${rowData.name} (attempt ${retryCount + 1})`);
                    setTimeout(() => updateTableWithMetadata(index, metadata, status, retryCount + 1), 1000);
                }
            }
        } catch (error) {
            console.error(`Critical error updating table for row ${rowData.name}:`, error);
            if (retryCount < 3) {
                console.log(`Retrying update for ${rowData.name} (attempt ${retryCount + 1})`);
                setTimeout(() => updateTableWithMetadata(index, metadata, status, retryCount + 1), 1000);
            }
        }
    }
}
// handleWorkerMessage function removed as parallel processing now handles workers efficiently
document.addEventListener('DOMContentLoaded', async function() {
    const existingModals = document.querySelectorAll('.modal');
    existingModals.forEach(modal => {
        const bsModalInstance = bootstrap.Modal.getInstance(modal);
        if (bsModalInstance) {
            bsModalInstance.dispose();
        }
        modal.remove();
    });
    // Remove any server-related localStorage items that are no longer needed
    localStorage.removeItem('csvision_server_path');
    localStorage.removeItem('csvision_use_web_workers');
    localStorage.removeItem('csvision_worker_count');

    // Fix accessibility issues with Bootstrap modals
    // Instead of removing aria-hidden, we'll use a better approach with inert attribute

    // When modal is about to be shown
    document.body.addEventListener('show.bs.modal', function(event) {
        const modal = event.target;
        if (modal && modal.classList.contains('modal')) {
            // Instead of removing aria-hidden, add data-accessibility-fix attribute
            // This will help us identify modals that need special handling
            modal.setAttribute('data-accessibility-fix', 'true');
        }
    });

    // When modal is fully shown
    document.body.addEventListener('shown.bs.modal', function(event) {
        const modal = event.target;
        if (modal && modal.classList.contains('modal')) {
            // Focus management - ensure focus is properly managed
            // Find the first focusable element in the modal and focus it
            setTimeout(() => {
                const focusableElements = modal.querySelectorAll(
                    'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
                );

                if (focusableElements.length > 0) {
                    // Focus the first element (usually close button or a primary action)
                    focusableElements[0].focus();
                }
            }, 100);
        }
    });

    // When modal is hidden, ensure we clean up
    document.body.addEventListener('hidden.bs.modal', function(event) {
        const modal = event.target;
        if (modal && modal.hasAttribute('data-accessibility-fix')) {
            modal.removeAttribute('data-accessibility-fix');
        }
    });

    // Patch Bootstrap's Modal.prototype for better accessibility
    if (typeof bootstrap !== 'undefined' && bootstrap.Modal) {
        const originalShow = bootstrap.Modal.prototype.show;
        bootstrap.Modal.prototype.show = function() {
            // Call the original show method
            const result = originalShow.apply(this, arguments);

            // Instead of removing aria-hidden, we'll ensure proper focus management
            if (this._element) {
                this._element.setAttribute('data-accessibility-fix', 'true');
            }

            return result;
        };
    }

    initializeTable();
    setupEventListeners();
    updateUIState();
    updateExportButtonsState();
    setTimeout(function () {
        if (table && table.initialized) {
            table.redraw(true);
        }
        if (typeof window.initPromptManager === 'function') {
            window.initPromptManager();
        }
        if (typeof window.initSettingsManager === 'function') {
            window.initSettingsManager();
        }
        if (typeof window.initCsvManager === 'function') {
            window.initCsvManager();
        }
    }, 500);
});
function formatCountColumn(rowData) {
    const keywords = rowData.keywords || "";
    const description = rowData.description || "";
    const keywordCount = keywords ? keywords.split(',').filter(k => k.trim()).length : 0;
    const charCount = description.length;
    const wordCount = description.trim() ? description.trim().split(/\s+/).length : 0;
    return `<div class="count-container">
              <span class="badge bg-primary" title="Description characters">${charCount} Char</span>
              <span class="badge bg-info" title="Description words">${wordCount} Words</span>
              <span class="badge bg-secondary" title="Keywords">${keywordCount} Keywords</span>
            </div>`;
}
function initializeTable() {
    table = new Tabulator("#metadataTable", {
        data: metadataResults,
        index: "name",
        layout: "fitColumns",
        pagination: "local",
        paginationSize: 10,
        paginationSizeSelector: [5, 10, 20, 50, true],
        movableColumns: true,
        selectable: true,
        movableRows:true,
        rowContextMenu: rowMenu,
        placeholder: "No Data Available - Please Select Images or Videos to Process",
        footerElement: `<div class="tabulator-footer-contents">
            <div class="stats-container">
                <div class="stat-item">
                    <span class="stat-label">Total Files:</span>
                    <span class="stat-value">0</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Videos:</span>
                    <span class="stat-value videos">0</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Completed:</span>
                    <span class="stat-value completed">0</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Pending:</span>
                    <span class="stat-value pending">0</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Queued:</span>
                    <span class="stat-value queued">0</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Error:</span>
                    <span class="stat-value error">0</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Stopped:</span>
                    <span class="stat-value stopped">0</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Paused:</span>
                    <span class="stat-value paused">0</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Total Time (s):</span>
                    <span class="stat-value time">0.00</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Avg Time (s):</span>
                    <span class="stat-value time">0.00</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Processing (s):</span>
                    <span class="stat-value time">0.00</span>
                </div>
            </div>
        </div>`,
        columns: [
            {
                formatter: "rownum",
                hozAlign: "center",
                headerSort: false,
                width: 40,
                title: "No.",
                download: false,
                print: false
            },
            {
                title: "Preview",
                field: "preview",
                formatter: "html",
                width: 120,
                headerSort: false,
                hozAlign: "center",
                tooltip: true,
                download: false,
                print: false
            },
            {
                title: "Filename",
                field: "name",
                sorter: "string",
                headerFilter: "input",
                tooltip: function (e, cell, onRendered) {
                    return cell.getData().name;
                }
            },
            {
                title: "Title",
                field: "title",
                editor: "textarea",
                formatter: function(cell) {
                    const value = cell.getValue() || "";
                    const rowData = cell.getRow().getData();
                    return `<div class="field-with-actions">
                              <div class="field-content">${value}</div>
                              <div class="field-actions">
                                <button class="btn btn-sm btn-primary copy-btn bi bi-clipboard" title="Copy Title"></button>
                                <button class="btn btn-sm btn-warning fix-btn bi bi-wrench" title="Fix Title Format"></button>
                                <button class="btn btn-sm btn-success regenerate-btn bi bi-magic" data-field="title" title="${rowData.title && rowData.title.trim() ? 'Regenerate Title' : 'Generate Title'}"></button>
                              </div>
                            </div>`;
                },
                headerFilter: "input",
                tooltip: true,
                cellClick: function(e, cell) {
                    const target = e.target;
                    const rowData = cell.getRow().getData();

                    // Handle copy button click
                    if (target.closest('.copy-btn') || target.classList.contains('bi-clipboard')) {
                        e.stopPropagation();
                        navigator.clipboard.writeText(rowData.title || "");
                        showToast("Title copied to clipboard");
                        return;
                    }

                    // Handle fix button click
                    if (target.closest('.fix-btn') || target.classList.contains('bi-wrench')) {
                        e.stopPropagation();
                        fixTitleFormat(rowData);
                        return;
                    }

                    // Handle regenerate button click
                    if (target.closest('.regenerate-btn') || target.classList.contains('bi-magic')) {
                        e.stopPropagation();
                        regenerateMetadata(rowData, 'title');
                        return;
                    }
                }
            },
            {
                title: "Description",
                field: "description",
                editor: "textarea",
                formatter: function(cell) {
                    const value = cell.getValue() || "";
                    const rowData = cell.getRow().getData();
                    return `<div class="field-with-actions">
                              <div class="field-content">${value}</div>
                              <div class="field-actions">
                                <button class="btn btn-sm btn-primary copy-btn bi bi-clipboard" title="Copy Description"></button>
                                <button class="btn btn-sm btn-warning fix-btn bi bi-wrench" title="Fix Description Format"></button>
                                <button class="btn btn-sm btn-success regenerate-btn bi bi-magic" data-field="description" title="${rowData.description && rowData.description.trim() ? 'Regenerate Description' : 'Generate Description'}"></button>
                              </div>
                            </div>`;
                },
                headerFilter: "input",
                tooltip: true,
                cellClick: function(e, cell) {
                    const target = e.target;
                    const rowData = cell.getRow().getData();

                    // Handle copy button click
                    if (target.closest('.copy-btn') || target.classList.contains('bi-clipboard')) {
                        e.stopPropagation();
                        navigator.clipboard.writeText(rowData.description || "");
                        showToast("Description copied to clipboard");
                        return;
                    }

                    // Handle fix button click
                    if (target.closest('.fix-btn') || target.classList.contains('bi-wrench')) {
                        e.stopPropagation();
                        fixDescriptionFormat(rowData);
                        return;
                    }

                    // Handle regenerate button click
                    if (target.closest('.regenerate-btn') || target.classList.contains('bi-magic')) {
                        e.stopPropagation();
                        regenerateMetadata(rowData, 'description');
                        return;
                    }
                }
            },
            {
                title: "Keywords",
                field: "keywords",
                editor: "textarea",
                formatter: function(cell) {
                    const value = cell.getValue() || "";
                    const rowData = cell.getRow().getData();
                    return `<div class="field-with-actions">
                              <div class="field-content">${value}</div>
                              <div class="field-actions">
                                <button class="btn btn-sm btn-primary copy-btn bi bi-clipboard" title="Copy Keywords"></button>
                                <button class="btn btn-sm btn-warning fix-btn bi bi-wrench" title="Fix Keywords Format"></button>
                                <button class="btn btn-sm btn-success regenerate-btn bi bi-magic" data-field="keywords" title="${rowData.keywords && rowData.keywords.trim() ? 'Regenerate Keywords' : 'Generate Keywords'}"></button>
                              </div>
                            </div>`;
                },
                headerFilter: "input",
                tooltip: true,
                cellClick: function(e, cell) {
                    const target = e.target;
                    const rowData = cell.getRow().getData();

                    // Handle copy button click
                    if (target.closest('.copy-btn') || target.classList.contains('bi-clipboard')) {
                        e.stopPropagation();
                        navigator.clipboard.writeText(rowData.keywords || "");
                        showToast("Keywords copied to clipboard");
                        return;
                    }

                    // Handle fix button click
                    if (target.closest('.fix-btn') || target.classList.contains('bi-wrench')) {
                        e.stopPropagation();
                        fixKeywordsFormat(rowData);
                        return;
                    }

                    // Handle regenerate button click
                    if (target.closest('.regenerate-btn') || target.classList.contains('bi-magic')) {
                        e.stopPropagation();
                        regenerateMetadata(rowData, 'keywords');
                        return;
                    }
                }
            },
            {
                title: "Categories",
                field: "shutterstockCategories",
                editor: "textarea",
                formatter: function(cell) {
                    const value = cell.getValue() || "";
                    const rowData = cell.getRow().getData();
                    const categoriesValue = rowData.shutterstockCategories || "";
                    const hasCategories = categoriesValue && typeof categoriesValue === 'string' && categoriesValue.trim();
                    return `<div class="field-with-actions">
                              <div class="field-content">${value}</div>
                              <div class="field-actions">
                                <button class="btn btn-sm btn-primary copy-btn bi bi-clipboard" title="Copy Categories"></button>
                                <button class="btn btn-sm btn-warning fix-btn bi bi-wrench" title="Fix Categories Format"></button>
                                <button class="btn btn-sm btn-success regenerate-btn bi bi-magic" data-field="shutterstockCategories" title="${hasCategories ? 'Regenerate Categories' : 'Generate Categories'}"></button>
                              </div>
                            </div>`;
                },
                headerFilter: "input",
                tooltip: true,
                cellClick: function(e, cell) {
                    const target = e.target;
                    const rowData = cell.getRow().getData();

                    // Handle copy button click
                    if (target.closest('.copy-btn') || target.classList.contains('bi-clipboard')) {
                        e.stopPropagation();
                        navigator.clipboard.writeText(rowData.shutterstockCategories || "");
                        showToast("Categories copied to clipboard");
                        return;
                    }

                    // Handle fix button click
                    if (target.closest('.fix-btn') || target.classList.contains('bi-wrench')) {
                        e.stopPropagation();
                        fixCategoriesFormat(rowData);
                        return;
                    }

                    // Handle regenerate button click
                    if (target.closest('.regenerate-btn') || target.classList.contains('bi-magic')) {
                        e.stopPropagation();
                        regenerateMetadata(rowData, 'shutterstockCategories');
                        return;
                    }
                }
            },
            {
                title: "Category",
                field: "adobestockCategory",
                editor: "textarea",
                formatter: function(cell) {
                    const value = cell.getValue() || "";
                    const rowData = cell.getRow().getData();
                    const categoryValue = rowData.adobestockCategory || "";
                    const hasCategory = categoryValue && typeof categoryValue === 'string' && categoryValue.trim();
                    return `<div class="field-with-actions">
                              <div class="field-content">${value}</div>
                              <div class="field-actions">
                                <button class="btn btn-sm btn-primary copy-btn bi bi-clipboard" title="Copy Category"></button>
                                <button class="btn btn-sm btn-warning fix-btn bi bi-wrench" title="Fix Category Format"></button>
                                <button class="btn btn-sm btn-success regenerate-btn bi bi-magic" data-field="adobestockCategory" title="${hasCategory ? 'Regenerate Category' : 'Generate Category'}"></button>
                              </div>
                            </div>`;
                },
                headerFilter: "input",
                tooltip: true,
                cellClick: function(e, cell) {
                    const target = e.target;
                    const rowData = cell.getRow().getData();

                    // Handle copy button click
                    if (target.closest('.copy-btn') || target.classList.contains('bi-clipboard')) {
                        e.stopPropagation();
                        navigator.clipboard.writeText(rowData.adobestockCategory || "");
                        showToast("Category copied to clipboard");
                        return;
                    }

                    // Handle fix button click
                    if (target.closest('.fix-btn') || target.classList.contains('bi-wrench')) {
                        e.stopPropagation();
                        fixCategoryFormat(rowData);
                        return;
                    }

                    // Handle regenerate button click
                    if (target.closest('.regenerate-btn') || target.classList.contains('bi-magic')) {
                        e.stopPropagation();
                        regenerateMetadata(rowData, 'adobestockCategory');
                        return;
                    }
                }
            },
            {
                title: "Info",
                field: "info",
                hozAlign: "center",
                width: 180,
                formatter: function (cell) {
                    const rowData = cell.getRow().getData();
                    const status = rowData.status || "Pending";
                    const statusHTML = getStatusHTML(status);
                    const countHTML = formatCountColumn(rowData);

                    return `<div class="info-column">
                              <div class="status-section">${statusHTML}</div>
                              <div class="count-section">${countHTML}</div>
                              <div class="regenerate-all-section">
                                <button class="btn btn-sm btn-primary-purple regenerate-all-btn bi bi-magic" title="Regenerate All Metadata">
                                    Regenerate All
                                </button>
                              </div>
                            </div>`;
                },
                headerSort: true,
                sorter: function (a, b, aRow, bRow) {
                    // Sort primarily by status
                    const aData = aRow.getData();
                    const bData = bRow.getData();

                    // Define status priority (completed first, then pending, then errors)
                    const statusPriority = {
                        "Completed": 1,
                        "Pending": 2,
                        "Queued": 3,
                        "Processing": 4,
                        "Paused": 5,
                        "Stopped": 6
                    };

                    // Get status priority (default to 99 for unknown status)
                    const aStatusPriority = statusPriority[aData.status] ||
                                           (aData.status && aData.status.startsWith("Error") ? 7 : 99);
                    const bStatusPriority = statusPriority[bData.status] ||
                                           (bData.status && bData.status.startsWith("Error") ? 7 : 99);

                    // If status is different, sort by status priority
                    if (aStatusPriority !== bStatusPriority) {
                        return aStatusPriority - bStatusPriority;
                    }

                    // If status is the same, sort by keyword count as secondary criteria
                    const aKeywords = aData.keywords;
                    const bKeywords = bData.keywords;
                    const aCount = aKeywords ? aKeywords.split(',').filter(k => k.trim()).length : 0;
                    const bCount = bKeywords ? bKeywords.split(',').filter(k => k.trim()).length : 0;
                    return bCount - aCount; // Higher count first
                },
                cellClick: function(e, cell) {
                    const target = e.target;
                    const rowData = cell.getRow().getData();

                    // Handle regenerate all button click
                    if (target.closest('.regenerate-all-btn') ||
                        (target.classList.contains('bi-magic') && target.parentElement.classList.contains('regenerate-all-btn'))) {
                        e.stopPropagation();
                        regenerateMetadata(rowData, 'all');
                        return;
                    }
                },
                download: false,
                print: false
            }
        ],
        rowFormatter: function (row) {
            const data = row.getData();
            if (data.status === "Completed") {
                row.getElement().style.backgroundColor = "#f8fff8";
            } else if (data.status && data.status.includes("Error")) {
                row.getElement().style.backgroundColor = "#fff8f8";
            } else if (data.status === "Cancelled") {
                row.getElement().style.backgroundColor = "#fff8f0";
            } else {
                row.getElement().style.backgroundColor = "";
            }
        }
    });
    table.on("dataChanged", function() {
        updateTableFooter();
    });
    table.on("rowUpdated", function() {
        updateTableFooter();
    });
    table.on("cellClick", function (e, cell) {
        const column = cell.getColumn().getField();
        if (column === "preview") {
            showImagePreview(cell.getRow().getData());
        }
    });
    table.on("tableBuilt", function() {
        const footer = document.querySelector("#metadataTable .tabulator-footer");
        if (footer) {
            const paginator = footer.querySelector(".tabulator-paginator");
            if (paginator) {
                const statsContainer = footer.querySelector(".stats-container");
                if (statsContainer) {
                    const paginatorWrapper = document.createElement('div');
                    paginatorWrapper.appendChild(paginator);
                    statsContainer.parentNode.insertBefore(paginatorWrapper, statsContainer.nextSibling);
                }
            }
        }
        const statusStopBtn = document.getElementById('statusStopBtn');
        if (statusStopBtn) {
            statusStopBtn.addEventListener('click', function(e) {
                e.stopPropagation();
                stopProcessing();
            });
        }
        setupContextMenuHoverBehavior();
    });
    table.on("cellEdited", function (cell) {
        const column = cell.getColumn().getField();
        const rowData = cell.getRow().getData();
        const rowIndex = metadataResults.findIndex(item => item.name === rowData.name);
        if (rowIndex === -1) return;
        let needsUpdate = false;
        if (column === "keywords") {
            let keywords = cell.getValue();
            const maxKeywords = getMaxKeywordsFromSettings();
            const needsCleaning = /[;|•\t\n\r\/\-–—&]/.test(keywords) ||
                /\s+and\s+/i.test(keywords) ||
                /,\s*,/.test(keywords);
            if (needsCleaning) {
                keywords = cleanKeywordsFormat(keywords, maxKeywords);
                cell.setValue(keywords);
            } else {
                const cleanKeywords = ensureLowercaseKeywords(keywords);
                if (cleanKeywords !== keywords) {
                    cell.setValue(cleanKeywords);
                    keywords = cleanKeywords;
                }
            }
            const keywordArray = keywords.split(',').map(k => k.trim()).filter(k => k);
            if (maxKeywords > 0 && keywordArray.length > maxKeywords) {
                keywords = keywordArray.slice(0, maxKeywords).join(', ');
                cell.setValue(keywords);
            }
            if (metadataResults[rowIndex].keywords !== keywords) {
                metadataResults[rowIndex].keywords = keywords;
                needsUpdate = true;
            }
        } else if (column === "description") {
            let description = cell.getValue();
            const needsCleaning = /(?:description:|keywords:|and\s+keywords|following your rules)/i.test(description);
            if (needsCleaning) {
                description = cleanDescriptionText(description);
                cell.setValue(description);
            }
            if (metadataResults[rowIndex].description !== description) {
                metadataResults[rowIndex].description = description;
                needsUpdate = true;
            }
        } else if (column === "title") {
            const title = cell.getValue();
            if (metadataResults[rowIndex].title !== title) {
                metadataResults[rowIndex].title = title;
                needsUpdate = true;
            }
        } else if (column === "shutterstockCategories") {
            const categories = cell.getValue();
            if (metadataResults[rowIndex].shutterstockCategories !== categories) {
                metadataResults[rowIndex].shutterstockCategories = categories;
                needsUpdate = true;
            }
        } else if (column === "adobestockCategory") {
            const category = cell.getValue();
            if (metadataResults[rowIndex].adobestockCategory !== category) {
                metadataResults[rowIndex].adobestockCategory = category;
                needsUpdate = true;
            }
        }
        if (needsUpdate) {
            const updatedRow = { ...metadataResults[rowIndex] };
            table.updateData([updatedRow])
                .then(() => {
                    const row = table.getRow(rowData.name);
                    if (row) {
                        const infoCell = row.getCell('info');
                        if (infoCell) {
                            infoCell.setValue(updatedRow);
                        }
                    }
                })
                .catch(err => console.error("Error updating row data:", err));
        }
    });
    table.on("cellEditComplete", function(cell) {
        const rowData = cell.getRow().getData();
        const row = table.getRow(rowData.name);
        if (row) {
            const infoCell = row.getCell('info');
            if (infoCell) {
                infoCell.setValue(rowData);
            }
        }
    });
    table.on("headerClick", function(e, column) {
        if (column.getField() === "status") {
            const target = e.target;
            if (target.closest('#statusStopBtn') || target.id === 'statusStopBtn') {
                e.stopPropagation();
                stopProcessing();
                return false;
            }
        }
    });
    table.on("rowContextMenu", function(e, row) {
        const menu = document.querySelector('.tabulator-menu');
        if (menu) {
            const rect = menu.getBoundingClientRect();
            const windowHeight = window.innerHeight;
            const windowWidth = window.innerWidth;
            if (rect.bottom > windowHeight) {
                menu.style.top = (windowHeight - rect.height) + 'px';
            }
            if (rect.right > windowWidth) {
                menu.style.left = (windowWidth - rect.width) + 'px';
            }
        }
    });
}
const rowMenu = [
    {
        label: "--- Batch Actions ---",
        disabled: true,
        cssClass: "menu-group-header"
    },
    {
        label: "Fix All Rows Description Format",
        action: function (e, row) {
            fixAllRowsDescriptionFormat();
        }
    },
    {
        label: "Fix All Rows Title Format",
        action: function (e, row) {
            fixAllRowsTitleFormat();
        }
    },
    {
        label: "Fix All Rows Keywords Format",
        action: function (e, row) {
            fixAllRowsKeywordsFormat();
        }
    },
    {
        label: "Keywords to Lowercase All Rows",
        action: function (e, row) {
            convertKeywordsCaseAllRows('lowercase');
        }
    },
    {
        label: "Keywords to Title Case All Rows",
        action: function (e, row) {
            convertKeywordsCaseAllRows('titlecase');
        }
    },
    { separator: true },
    {
        label: "--- Row Actions ---",
        disabled: true,
        cssClass: "menu-group-header"
    },
    {
        label: "View Image",
        action: function (e, row) {
            showImagePreview(row.getData());
        }
    },
    {
        label: function(row) {
            const data = row.getData();
            return (data.title && data.title.trim() && data.description && data.description.trim() && data.keywords && data.keywords.trim())
                ? "Regenerate All Rows"
                : "Generate All Rows";
        },
        action: function (e, row) {
            regenerateAllRows();
        }
    },
    {
        label: function(row) {
            const data = row.getData();
            return (data.title && data.title.trim() && data.description && data.description.trim() && data.keywords && data.keywords.trim())
                ? "Regenerate Selected Rows"
                : "Generate Selected Rows";
        },
        action: function (e, row) {
            regenerateSelectedRows();
        }
    },
    {
        label: "Regenerate Error Rows",
        action: function (e, row) {
            regenerateErrorRows();
        }
    },
    {
        label: "Delete Row",
        action: function (e, row) {
            deleteRow(row.getData());
        }
    }
];
/**
 * Fix the format of keywords for a row
 * @param {Object} rowData - The row data object
 */
function fixKeywordsFormat(rowData) {
    if (!rowData || !rowData.keywords) {
        showToast("No keywords to fix", "warning");
        return;
    }

    const rowIndex = metadataResults.findIndex(item => item.name === rowData.name);
    if (rowIndex === -1) {
        showToast("Row not found", "error");
        return;
    }

    const maxKeywords = getMaxKeywordsFromSettings();
    const originalKeywords = rowData.keywords;
    const cleanedKeywords = cleanKeywordsFormat(originalKeywords, maxKeywords);

    if (cleanedKeywords !== originalKeywords) {
        // Update the data in memory first
        metadataResults[rowIndex].keywords = cleanedKeywords;

        // Create a new row data object with only the updated field
        const updatedData = {
            name: rowData.name,
            keywords: cleanedKeywords
        };

        // Use a safer approach with a timeout to avoid race conditions
        setTimeout(() => {
            try {
                // Use the table's updateOrAddData method which is more reliable
                table.updateOrAddData([updatedData])
                    .then(() => {
                        // Force a redraw of the table to ensure UI is updated
                        table.redraw(true);
                        showToast("Keywords format fixed successfully", "success");
                    })
                    .catch(err => {
                        console.error("Error updating table data:", err);
                        showToast("Keywords format fixed in memory", "success");
                    });
            } catch (error) {
                console.error("Error during table update:", error);
                showToast("Keywords format fixed in memory", "success");
            }
        }, 100);
    } else {
        showToast("Keywords already in the correct format", "info");
    }
}

/**
 * Fix the format of description for a row
 * @param {Object} rowData - The row data object
 */
function fixDescriptionFormat(rowData) {
    if (!rowData || !rowData.description) {
        showToast("No description to fix", "warning");
        return;
    }

    const rowIndex = metadataResults.findIndex(item => item.name === rowData.name);
    if (rowIndex === -1) {
        showToast("Row not found", "error");
        return;
    }

    const originalDescription = rowData.description;
    const cleanedDescription = cleanDescriptionText(originalDescription);

    if (cleanedDescription !== originalDescription) {
        // Update the data in memory first
        metadataResults[rowIndex].description = cleanedDescription;

        // Create a new row data object with only the updated field
        const updatedData = {
            name: rowData.name,
            description: cleanedDescription
        };

        // Use a safer approach with a timeout to avoid race conditions
        setTimeout(() => {
            try {
                // Use the table's updateOrAddData method which is more reliable
                table.updateOrAddData([updatedData])
                    .then(() => {
                        // Force a redraw of the table to ensure UI is updated
                        table.redraw(true);
                        showToast("Description format fixed successfully", "success");
                    })
                    .catch(err => {
                        console.error("Error updating table data:", err);
                        showToast("Description format fixed in memory", "success");
                    });
            } catch (error) {
                console.error("Error during table update:", error);
                showToast("Description format fixed in memory", "success");
            }
        }, 100);
    } else {
        showToast("Description already in the correct format", "info");
    }
}

/**
 * Fix the format of title for a row
 * @param {Object} rowData - The row data object
 */
function fixTitleFormat(rowData) {
    if (!rowData || !rowData.title) {
        showToast("No title to fix", "warning");
        return;
    }

    const rowIndex = metadataResults.findIndex(item => item.name === rowData.name);
    if (rowIndex === -1) {
        showToast("Row not found", "error");
        return;
    }

    const originalTitle = rowData.title;
    // Clean title by removing common prefixes and formatting issues
    let cleanedTitle = originalTitle
        .replace(/^(title:|image title:|photo title:)\s*/i, "")
        .replace(/^(here is|here's|sure, here is|okay, here is|certainly, here is)\s*(the\s*)?(title).*?:?\s*/i, "")
        .replace(/^(?:I have|I've) generated the title.*?:?\s*/i, "")
        .replace(/^(?:the title for the image is|title of the image:)\s*/i, "")
        .replace(/^(?:tailored to your specifications|created as requested|as per your request|according to your requirements).*?:/i, "")
        .replace(/^(?:here is|here are|following your rules|following all the rules|as requested).*?:/i, "")
        .replace(/^(?:I've|I have)\s+created\s+(?:a|an|the)?\s+title.*?:/i, "")
        .replace(/^(?:As requested|As per your request),?\s+(?:here is|here are|I've provided|I have provided).*?:/i, "")
        .trim();

    // Handle case where title has a colon prefix that might be metadata
    const colonPrefixMatch = cleanedTitle.match(/^([^.!?\n]{5,50}?):\s+/);
    if (colonPrefixMatch) {
        const prefix = colonPrefixMatch[1].toLowerCase();
        const metaWords = ['title', 'image', 'photo', 'following', 'requested', 'instruction', 'guideline', 'rule'];
        const containsMetaWord = metaWords.some(word => prefix.includes(word));
        if (containsMetaWord || prefix.length < 10) {
            cleanedTitle = cleanedTitle.replace(/^[^.!?\n]{5,50}?:\s+/, '');
        }
    }

    // Fix title case for the title (capitalize first letter of each word)
    cleanedTitle = cleanedTitle.replace(/\w\S*/g, (txt) => {
        return txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase();
    });

    if (cleanedTitle !== originalTitle) {
        // Update the data in memory first
        metadataResults[rowIndex].title = cleanedTitle;

        // Create a new row data object with only the updated field
        const updatedData = {
            name: rowData.name,
            title: cleanedTitle
        };

        // Use a safer approach with a timeout to avoid race conditions
        setTimeout(() => {
            try {
                // Use the table's updateOrAddData method which is more reliable
                table.updateOrAddData([updatedData])
                    .then(() => {
                        // Force a redraw of the table to ensure UI is updated
                        table.redraw(true);
                        showToast("Title format fixed successfully", "success");
                    })
                    .catch(err => {
                        console.error("Error updating table data:", err);
                        showToast("Title format fixed in memory", "success");
                    });
            } catch (error) {
                console.error("Error during table update:", error);
                showToast("Title format fixed in memory", "success");
            }
        }, 100);
    } else {
        showToast("Title already in the correct format", "info");
    }
}

/**
 * Fix the format of categories for a row
 * @param {Object} rowData - The row data object
 */
function fixCategoriesFormat(rowData) {
    if (!rowData || !rowData.shutterstockCategories) {
        showToast("No categories to fix", "warning");
        return;
    }

    const rowIndex = metadataResults.findIndex(item => item.name === rowData.name);
    if (rowIndex === -1) {
        showToast("Row not found", "error");
        return;
    }

    const originalCategories = rowData.shutterstockCategories;
    // Basic cleaning for categories - remove extra spaces and normalize commas
    let cleanedCategories = originalCategories
        .replace(/\s*,\s*/g, ', ')  // Normalize comma spacing
        .replace(/,+/g, ',')        // Remove duplicate commas
        .replace(/^,|,$/g, '')      // Remove leading/trailing commas
        .trim();

    if (cleanedCategories !== originalCategories) {
        // Update the data in memory first
        metadataResults[rowIndex].shutterstockCategories = cleanedCategories;

        // Create a new row data object with only the updated field
        const updatedData = {
            name: rowData.name,
            shutterstockCategories: cleanedCategories
        };

        // Use a safer approach with a timeout to avoid race conditions
        setTimeout(() => {
            try {
                // Use the table's updateOrAddData method which is more reliable
                table.updateOrAddData([updatedData])
                    .then(() => {
                        // Force a redraw of the table to ensure UI is updated
                        table.redraw(true);
                        showToast("Categories format fixed successfully", "success");
                    })
                    .catch(err => {
                        console.error("Error updating table data:", err);
                        showToast("Categories format fixed in memory", "success");
                    });
            } catch (error) {
                console.error("Error during table update:", error);
                showToast("Categories format fixed in memory", "success");
            }
        }, 100);
    } else {
        showToast("Categories already in the correct format", "info");
    }
}

/**
 * Fix the format of category for a row
 * @param {Object} rowData - The row data object
 */
function fixCategoryFormat(rowData) {
    if (!rowData || !rowData.adobestockCategory) {
        showToast("No category to fix", "warning");
        return;
    }

    const rowIndex = metadataResults.findIndex(item => item.name === rowData.name);
    if (rowIndex === -1) {
        showToast("Row not found", "error");
        return;
    }

    const originalCategory = rowData.adobestockCategory;
    // Basic cleaning for category - remove extra spaces
    let cleanedCategory = originalCategory.trim();

    if (cleanedCategory !== originalCategory) {
        // Update the data in memory first
        metadataResults[rowIndex].adobestockCategory = cleanedCategory;

        // Create a new row data object with only the updated field
        const updatedData = {
            name: rowData.name,
            adobestockCategory: cleanedCategory
        };

        // Use a safer approach with a timeout to avoid race conditions
        setTimeout(() => {
            try {
                // Use the table's updateOrAddData method which is more reliable
                table.updateOrAddData([updatedData])
                    .then(() => {
                        // Force a redraw of the table to ensure UI is updated
                        table.redraw(true);
                        showToast("Category format fixed successfully", "success");
                    })
                    .catch(err => {
                        console.error("Error updating table data:", err);
                        showToast("Category format fixed in memory", "success");
                    });
            } catch (error) {
                console.error("Error during table update:", error);
                showToast("Category format fixed in memory", "success");
            }
        }, 100);
    } else {
        showToast("Category already in the correct format", "info");
    }
}

/**
 * Fix the format of titles for all rows
 */
function fixAllRowsTitleFormat() {
    if (!metadataResults || metadataResults.length === 0) {
        showToast("No data to process", "warning");
        return;
    }

    let updatedRows = [];
    let fixedCount = 0;

    metadataResults.forEach((rowData, index) => {
        if (!rowData.title) return;

        // Clean title by removing common prefixes and formatting issues
        let cleanedTitle = rowData.title
            .replace(/^(title:|image title:|photo title:)\s*/i, "")
            .replace(/^(here is|here's|sure, here is|okay, here is|certainly, here is)\s*(the\s*)?(title).*?:?\s*/i, "")
            .replace(/^(?:I have|I've) generated the title.*?:?\s*/i, "")
            .replace(/^(?:the title for the image is|title of the image:)\s*/i, "")
            .replace(/^(?:tailored to your specifications|created as requested|as per your request|according to your requirements).*?:/i, "")
            .replace(/^(?:here is|here are|following your rules|following all the rules|as requested).*?:/i, "")
            .replace(/^(?:I've|I have)\s+created\s+(?:a|an|the)?\s+title.*?:/i, "")
            .replace(/^(?:As requested|As per your request),?\s+(?:here is|here are|I've provided|I have provided).*?:/i, "")
            .trim();

        // Handle case where title has a colon prefix that might be metadata
        const colonPrefixMatch = cleanedTitle.match(/^([^.!?\n]{5,50}?):\s+/);
        if (colonPrefixMatch) {
            const prefix = colonPrefixMatch[1].toLowerCase();
            const metaWords = ['title', 'image', 'photo', 'following', 'requested', 'instruction', 'guideline', 'rule'];
            const containsMetaWord = metaWords.some(word => prefix.includes(word));
            if (containsMetaWord || prefix.length < 10) {
                cleanedTitle = cleanedTitle.replace(/^[^.!?\n]{5,50}?:\s+/, '');
            }
        }

        // Fix title case for the title (capitalize first letter of each word)
        cleanedTitle = cleanedTitle.replace(/\w\S*/g, (txt) => {
            return txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase();
        });

        if (cleanedTitle !== rowData.title) {
            metadataResults[index].title = cleanedTitle;
            updatedRows.push({
                name: rowData.name,
                title: cleanedTitle
            });
            fixedCount++;
        }
    });

    if (updatedRows.length > 0) {
        try {
            // Use a timeout to allow any ongoing DOM operations to complete
            setTimeout(() => {
                try {
                    // Use the table's updateOrAddData method which is more reliable
                    table.updateOrAddData(updatedRows)
                        .then(() => {
                            // Force a redraw of the table to ensure UI is updated
                            table.redraw(true);
                            showToast(`Fixed title format for ${fixedCount} rows`, "success");
                        })
                        .catch(err => {
                            console.error("Error updating table data:", err);
                            showToast(`Fixed ${fixedCount} titles in memory, but table update failed`, "warning");
                        });
                } catch (error) {
                    console.error("Error during batch table update:", error);
                    showToast(`Fixed ${fixedCount} titles in memory, but table update failed`, "warning");
                }
            }, 100);
        } catch (error) {
            console.error("Error preparing table update:", error);
            showToast(`Fixed ${fixedCount} titles in memory, but table update failed`, "warning");
        }
    } else {
        showToast("All titles already in the correct format", "info");
    }
}

/**
 * Fix the format of descriptions for all rows
 */
function fixAllRowsDescriptionFormat() {
    if (!metadataResults || metadataResults.length === 0) {
        showToast("No data to process", "warning");
        return;
    }

    let updatedRows = [];
    let fixedCount = 0;

    metadataResults.forEach((rowData, index) => {
        if (!rowData.description) return;

        const originalDescription = rowData.description;
        const cleanedDescription = cleanDescriptionText(originalDescription);

        if (cleanedDescription !== originalDescription) {
            metadataResults[index].description = cleanedDescription;
            updatedRows.push({
                name: rowData.name,
                description: cleanedDescription
            });
            fixedCount++;
        }
    });

    if (updatedRows.length > 0) {
        try {
            // Use a timeout to allow any ongoing DOM operations to complete
            setTimeout(() => {
                try {
                    // Use the table's updateOrAddData method which is more reliable
                    table.updateOrAddData(updatedRows)
                        .then(() => {
                            // Force a redraw of the table to ensure UI is updated
                            table.redraw(true);
                            showToast(`Fixed description format for ${fixedCount} rows`, "success");
                        })
                        .catch(err => {
                            console.error("Error updating table data:", err);
                            showToast(`Fixed ${fixedCount} descriptions in memory, but table update failed`, "warning");
                        });
                } catch (error) {
                    console.error("Error during batch table update:", error);
                    showToast(`Fixed ${fixedCount} descriptions in memory, but table update failed`, "warning");
                }
            }, 100);
        } catch (error) {
            console.error("Error preparing table update:", error);
            showToast(`Fixed ${fixedCount} descriptions in memory, but table update failed`, "warning");
        }
    } else {
        showToast("All descriptions already in the correct format", "info");
    }
}

/**
 * Fix the format of keywords for all rows
 */
function fixAllRowsKeywordsFormat() {
    if (!metadataResults || metadataResults.length === 0) {
        showToast("No data to process", "warning");
        return;
    }

    let updatedRows = [];
    let fixedCount = 0;
    const maxKeywords = getMaxKeywordsFromSettings();

    metadataResults.forEach((rowData, index) => {
        if (!rowData.keywords) return;

        const originalKeywords = rowData.keywords;
        const cleanedKeywords = cleanKeywordsFormat(originalKeywords, maxKeywords);

        if (cleanedKeywords !== originalKeywords) {
            metadataResults[index].keywords = cleanedKeywords;
            updatedRows.push({
                name: rowData.name,
                keywords: cleanedKeywords
            });
            fixedCount++;
        }
    });

    if (updatedRows.length > 0) {
        try {
            // Use a timeout to allow any ongoing DOM operations to complete
            setTimeout(() => {
                try {
                    // Use the table's updateOrAddData method which is more reliable
                    table.updateOrAddData(updatedRows)
                        .then(() => {
                            // Force a redraw of the table to ensure UI is updated
                            table.redraw(true);
                            showToast(`Fixed keywords format for ${fixedCount} rows`, "success");
                        })
                        .catch(err => {
                            console.error("Error updating table data:", err);
                            showToast(`Fixed ${fixedCount} keywords in memory, but table update failed`, "warning");
                        });
                } catch (error) {
                    console.error("Error during batch table update:", error);
                    showToast(`Fixed ${fixedCount} keywords in memory, but table update failed`, "warning");
                }
            }, 100);
        } catch (error) {
            console.error("Error preparing table update:", error);
            showToast(`Fixed ${fixedCount} keywords in memory, but table update failed`, "warning");
        }
    } else {
        showToast("All keywords already in the correct format", "info");
    }
}

function ensureLowercaseKeywords(keywords) {
    if (!keywords) return "";
    return keywords.split(',')
        .map(keyword => keyword.trim().toLowerCase())
        .join(', ');
}

/**
 * Convert keywords to lowercase or title case for a single row
 * @param {Object} rowData - The row data object
 * @param {string} caseType - The case type to convert to ('lowercase' or 'titlecase')
 */
function convertKeywordsCase(rowData, caseType) {
    if (!rowData || !rowData.keywords) {
        showToast("No keywords to convert", "warning");
        return;
    }

    const rowIndex = metadataResults.findIndex(item => item.name === rowData.name);
    if (rowIndex === -1) {
        showToast("Row not found", "error");
        return;
    }

    let keywords = rowData.keywords;
    let convertedKeywords = "";

    if (caseType === 'lowercase') {
        convertedKeywords = keywords.split(',')
            .map(keyword => keyword.trim().toLowerCase())
            .join(', ');
    } else if (caseType === 'titlecase') {
        convertedKeywords = keywords.split(',')
            .map(keyword => {
                return keyword.trim().replace(/\w\S*/g, (txt) => {
                    return txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase();
                });
            })
            .join(', ');
    }

    if (convertedKeywords !== keywords) {
        metadataResults[rowIndex].keywords = convertedKeywords;

        // Update the table
        const row = table.getRow(rowData.name);
        if (row) {
            row.update({ keywords: convertedKeywords })
                .then(() => {
                    // Update the info cell
                    const infoCell = row.getCell('info');
                    if (infoCell) {
                        infoCell.setValue(metadataResults[rowIndex]);
                    }
                    showToast(`Keywords converted to ${caseType === 'lowercase' ? 'lowercase' : 'title case'} successfully`, "success");
                })
                .catch(err => {
                    console.error("Error updating row:", err);
                    showToast("Error updating keywords", "error");
                });
        }
    } else {
        showToast("Keywords already in the requested format", "info");
    }
}

/**
 * Convert keywords to lowercase or title case for all rows
 * @param {string} caseType - The case type to convert to ('lowercase' or 'titlecase')
 */
function convertKeywordsCaseAllRows(caseType) {
    if (!metadataResults || metadataResults.length === 0) {
        showToast("No data to process", "warning");
        return;
    }

    let updatedRows = [];
    let convertedCount = 0;

    metadataResults.forEach((rowData, index) => {
        if (!rowData.keywords) return;

        let convertedKeywords = "";
        if (caseType === 'lowercase') {
            convertedKeywords = rowData.keywords.split(',')
                .map(keyword => keyword.trim().toLowerCase())
                .join(', ');
        } else if (caseType === 'titlecase') {
            convertedKeywords = rowData.keywords.split(',')
                .map(keyword => {
                    return keyword.trim().replace(/\w\S*/g, (txt) => {
                        return txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase();
                    });
                })
                .join(', ');
        }

        if (convertedKeywords !== rowData.keywords) {
            metadataResults[index].keywords = convertedKeywords;
            updatedRows.push({...metadataResults[index]});
            convertedCount++;
        }
    });

    if (updatedRows.length > 0) {
        table.updateData(updatedRows)
            .then(() => {
                // Update keyword count cells for all updated rows
                updatedRows.forEach(rowData => {
                    const row = table.getRow(rowData.name);
                    if (row) {
                        const infoCell = row.getCell('info');
                        if (infoCell) {
                            infoCell.setValue(rowData);
                        }
                    }
                });
                showToast(`Keywords converted to ${caseType === 'lowercase' ? 'lowercase' : 'title case'} for ${convertedCount} rows`, "success");
            })
            .catch(err => {
                console.error("Error updating table data:", err);
                showToast("Error updating keywords", "error");
            });
    } else {
        showToast("All keywords already in the requested format", "info");
    }
}
function calculateStats() {
    const totalFiles = metadataResults.length;
    const videoCount = metadataResults.filter(item => item.isVideo).length;
    const completed = metadataResults.filter(item => item.status === "Completed").length;
    const pending = metadataResults.filter(item => item.status === "Pending").length;
    const retrying = metadataResults.filter(item => item.status.startsWith("Retrying")).length;
    const queued = metadataResults.filter(item => item.status === "Queued").length;
    const error = metadataResults.filter(item => item.status.startsWith("Error")).length;
    const stopped = metadataResults.filter(item => item.status === "Stopped").length;
    const paused = metadataResults.filter(item => item.status === "Paused").length;
    const processingMode = localStorage.getItem('csvision_processing_mode') || 'sequential';
    const enableParallel = localStorage.getItem('csvision_enable_parallel') === 'true';
    const useWorkers = localStorage.getItem('csvision_use_web_workers') === 'true';
    const concurrency = parseInt(localStorage.getItem('csvision_concurrency') || '1');
    let totalTime = 0;
    let actualTotalTime = 0;

    // Calculate individual processing times
    totalTime = metadataResults.reduce((acc, item) => {
        if (item.status === "Completed" && item.processingTime) {
            return acc + parseFloat(item.processingTime);
        }
        return acc;
    }, 0);

    // Calculate actual total time for both parallel and sequential processing
    // using the same timestamp-based approach for consistency
    let firstStartTime = Infinity;
    let lastEndTime = 0;
    let validTimestamps = 0;

    // Find the earliest start time and latest end time
    metadataResults.forEach(item => {
        if (item.status === "Completed") {
            if (item.firstProcessedTime && item.firstProcessedTime < firstStartTime) {
                firstStartTime = item.firstProcessedTime;
            }

            if (item.lastProcessedTime && item.lastProcessedTime > lastEndTime) {
                lastEndTime = item.lastProcessedTime;
            }

            if (item.firstProcessedTime && item.lastProcessedTime) {
                validTimestamps++;
            }
        }
    });

    // Calculate wall-clock time for both parallel and sequential processing
    if (firstStartTime !== Infinity && lastEndTime > 0 && validTimestamps > 0) {
        // Calculate actual wall-clock time
        actualTotalTime = (lastEndTime - firstStartTime) / 1000;

        if (enableParallel && concurrency > 1) {
            // Adjust for concurrency - this is a more accurate representation
            // of the effective time saved by parallel processing
            const effectiveConcurrency = Math.min(concurrency, completed);
            if (effectiveConcurrency > 1) {
                actualTotalTime = totalTime / effectiveConcurrency;
                // Add overhead factor (20%)
                actualTotalTime *= 1.2;
            }
        } else {
            // For sequential processing, actual time equals sum of individual times
            actualTotalTime = totalTime;
        }
    } else {
        // Fallback if timestamps aren't available
        if (enableParallel && concurrency > 1) {
            // Estimate based on individual times and concurrency
            const effectiveConcurrency = Math.min(concurrency, completed);
            if (effectiveConcurrency > 1) {
                actualTotalTime = totalTime / effectiveConcurrency;
                // Add overhead factor (20%)
                actualTotalTime *= 1.2;
            } else {
                actualTotalTime = totalTime;
            }
        } else {
            // For sequential processing, actual time equals sum of individual times
            actualTotalTime = totalTime;
        }
    }

    const completedCount = completed || 1;

    // Calculate average time per image consistently across all modes
    // For parallel mode, use actualTotalTime which represents wall-clock time
    // For sequential mode, use totalTime which is the sum of individual processing times
    let avgTime;
    if (enableParallel && concurrency > 1) {
        avgTime = (actualTotalTime / completedCount).toFixed(2);
    } else {
        avgTime = (totalTime / completedCount).toFixed(2);
    }
    const currentTime = Date.now();

    // Calculate current processing time for active items
    let processingItems = metadataResults.filter(item => item.status === "Processing" && item.startTime);
    let processingTime = 0;

    if (processingItems.length > 0) {
        // Get individual processing times
        const processingTimes = processingItems.map(item => {
            return (currentTime - item.startTime) / 1000;
        });

        // For sequential or single item, just sum the times
        if (!enableParallel || concurrency <= 1 || processingItems.length <= 1) {
            processingTime = processingTimes.reduce((sum, time) => sum + time, 0);
        } else {
            // For parallel processing with multiple items:
            // Use the maximum time (representing the longest-running task)
            // as the actual wall-clock time
            processingTime = Math.max(...processingTimes);
        }
    }

    return {
        totalFiles,
        videoCount,
        completed,
        pending,
        retrying,
        queued,
        error,
        stopped,
        paused,
        totalTime: enableParallel && concurrency > 1 ? actualTotalTime.toFixed(2) : totalTime.toFixed(2),
        individualTotalTime: totalTime.toFixed(2),
        avgTime,
        processingTime: processingTime.toFixed(2),
        isParallel: enableParallel && concurrency > 1
    };
}
function updateTableFooter() {
    const stats = calculateStats();
    const footerElement = document.querySelector("#metadataTable .tabulator-footer");
    // isProcessing is a global variable that indicates if processing is active
    if (footerElement) {
        const statsContainer = footerElement.querySelector('.stats-container');
        if (statsContainer) {
            const totalTimeHtml = `
            <div class="stat-item">
                <span class="stat-label">Total Time (s):</span>
                <span class="stat-value time">${stats.totalTime}</span>
            </div>`;
            statsContainer.innerHTML = `
                <div class="stat-item">
                    <span class="stat-label">Total Files:</span>
                    <span class="stat-value">${stats.totalFiles}</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Videos:</span>
                    <span class="stat-value videos">${stats.videoCount}</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Completed:</span>
                    <span class="stat-value completed">${stats.completed}</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">${isProcessing ? "Retrying:" : "Pending:"}</span>
                    <span class="stat-value pending">${isProcessing ? stats.retrying : stats.pending}</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Queued:</span>
                    <span class="stat-value queued">${stats.queued}</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Error:</span>
                    <span class="stat-value error">${stats.error}</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Stopped:</span>
                    <span class="stat-value stopped">${stats.stopped}</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Paused:</span>
                    <span class="stat-value paused">${stats.paused}</span>
                </div>
                ${totalTimeHtml}
                <div class="stat-item" title="Average processing time per image">
                    <span class="stat-label">Avg Time (s):</span>
                    <span class="stat-value time">${stats.avgTime}</span>
                </div>
                <div class="stat-item" title="${stats.isParallel ? 'In parallel mode, this shows the wall-clock time of the longest-running task' : 'Total processing time of all active items'}">
                    <span class="stat-label">Processing (s):</span>
                    <span class="stat-value time">${stats.processingTime}</span>
                </div>
            `;
        }
    }
}
function stopProcessing() {
    if (!isProcessing) {
        showToast("No active processing to stop.", "warning");
        return;
    }

    // Immediately stop processing without confirmation
    isProcessing = false;
    isPaused = false;
    shouldStopProcessing = true;
    updateProcessingUI(false);
    showToast("Processing stopped. All pending and processing items have been stopped.", "info");

    // Stop parallel processor if available
    if (window.parallelProcessor) {
        window.parallelProcessor.stopProcessing();
        window.parallelProcessor.terminateWorkers();
        window.parallelProcessor = null;
    }

    // Stop web worker if available
    if (window.worker) {
        window.worker.terminate();
        window.worker = null;
    }

    // Update status for all in-progress items
    metadataResults.forEach((item, index) => {
        if (["Pending", "Queued", "Processing", "Paused"].includes(item.status) || item.status.startsWith("Retrying")) {
            // Calculate processing time for items that were being processed
            if (item.status === "Processing" && item.startTime) {
                const currentTime = Date.now();
                item.processingTime = (currentTime - item.startTime) / 1000;
                item.startTime = null;
            }
            updateTableWithMetadata(index, null, "Stopped");
        }
    });

    updateTableFooter();
}